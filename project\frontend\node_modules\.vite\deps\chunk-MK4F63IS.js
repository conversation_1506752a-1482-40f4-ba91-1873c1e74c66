import {
  require_baseFlatten
} from "./chunk-ICXN6OJ6.js";
import {
  require_overRest,
  require_setToString
} from "./chunk-6U5UHEYX.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/flatten.js
var require_flatten = __commonJS({
  "node_modules/lodash/flatten.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    function flatten(array) {
      var length = array == null ? 0 : array.length;
      return length ? baseFlatten(array, 1) : [];
    }
    module.exports = flatten;
  }
});

// node_modules/lodash/_flatRest.js
var require_flatRest = __commonJS({
  "node_modules/lodash/_flatRest.js"(exports, module) {
    var flatten = require_flatten();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function flatRest(func) {
      return setToString(overRest(func, void 0, flatten), func + "");
    }
    module.exports = flatRest;
  }
});

export {
  require_flatRest
};
//# sourceMappingURL=chunk-MK4F63IS.js.map
