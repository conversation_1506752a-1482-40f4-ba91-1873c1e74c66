/* eslint-disable no-param-reassign */
import { useI18n } from 'vue-i18n-composable';

import { runCheckerByName } from '@ekyc/helpers/face-action-checkers/face-action-checker-3d';
import { getActionList } from '@ekyc/services/ekyc';

import { useEkycSettings } from '../use-ekyc-settings';
import { useHistory } from './use-history';

type PossibleState = 'checking' | 'ending';
type PossibleAction = Types.FaceActionPossibleValue | Types.ActionSequencePossibleValue;

const actionList = ref<Types.FaceActionPossibleValue[][]>([]);
const actionListIndex = ref<number>(-1);

const sequenceList = ref<Types.ActionSequencePossibleValue[]>([]);
const sequenceListIndex = ref<number>(-1);

const state = ref<PossibleState>('checking');
const nextState = ref<PossibleState>('checking');
const actionProgress = ref<number>(0);
const logs = ref<any[][]>([]);

export const useFaceAction = () => {
  const { t } = useI18n();
  const { allSettings } = useEkycSettings();
  const { detectionHistory } = useHistory();

  const currentAction = computed<PossibleAction>(() => {
    if (actionListIndex.value < actionList.value.length) {
      return actionList.value[actionListIndex.value]?.[0];
    }
    if (sequenceListIndex.value < sequenceList.value.length) {
      return sequenceList.value[sequenceListIndex.value];
    }
    return null;
  });

  const shouldSaveSnapshot = computed(() => {
    if (actionListIndex.value < actionList.value.length) {
      return true;
    }
    return false;
  });

  const numActionsToShow = computed(
    (): number => actionList.value.filter(as => as[0] !== 'idle_background').length,
  );

  const actionIconSetName = computed(() => allSettings.liveness.liveness.actionIconSetName);

  const actionDescription = computed<string>(() =>
    currentAction.value ? (t(`ekyc.video.actions.${currentAction.value}`) as string) : '',
  );
  const actionInstruction = computed<string>(
    () => t(`ekyc.video.instructions.${currentAction.value}`) as string,
  );

  const enableIdleOnly = computed<boolean>(() => allSettings.liveness.liveness.enableIdleOnly);
  const enableFaceSize = computed<boolean>(() => allSettings.liveness.liveness.enableFaceSize);

  async function loadActionList({ log = {} }) {
    console.log('[Liveness] Actionlist loading...');
    const actions = await getActionList({ log });
    actionList.value = actions.actionList;
    sequenceList.value = actions.actionSequence;
    console.log('[Liveness] Actionlist loaded...');
  }

  function getNextAction() {
    state.value = 'checking';
    nextState.value = 'checking';
    actionListIndex.value += 1;
    if (actionListIndex.value >= actionList.value.length) {
      sequenceListIndex.value += 1;
    }
  }

  function checkAction() {
    state.value = nextState.value;
    const action = currentAction.value;
    const orLogs: Types.MethodLog[][] = [];

    const threshold = { ...(allSettings.liveness.liveness.threshold[action] ?? {}) };

    const somePassed = allSettings.liveness.liveness.checkers[action].some(
      (checker: Types.PossibleChecker[]) => {
        const andLogs: Types.MethodLog[] = [];
        const andPassed = checker.every(method => {
          const log: Types.MethodLog = {
            passed: false,
            progress: 0,
            action: action,
            method,
            threshold,
          };
          const {
            passed,
            progress,
            log: idleLog,
          } = runCheckerByName(
            action,
            method,
            detectionHistory.value as Types.FaceDetectionResult[],
          );
          log.passed = passed;
          log.progress = progress;
          Object.assign(log, idleLog);
          andLogs.push(log);
          actionProgress.value = progress;
          return passed;
        });
        orLogs.push(andLogs);
        return andPassed;
      },
    );

    logs.value = orLogs;

    if (somePassed) {
      nextState.value = 'ending';
    } else {
      nextState.value = state.value;
    }

    return {
      passed: nextState.value === 'ending',
      state: state.value,
      nextState: nextState.value,
      logs: orLogs,
    };
  }

  function setToNotPassState() {
    state.value = 'checking';
    nextState.value = 'checking';
  }

  function restartCurrentProgress() {
    actionProgress.value = 0;
  }

  function restartFaceAction() {
    actionListIndex.value = -1;
    sequenceListIndex.value = -1;
    state.value = 'checking';
    nextState.value = 'checking';
    restartCurrentProgress();
    getNextAction();
  }

  function resetFaceAction() {
    actionList.value = [];
    actionListIndex.value = -1;
    sequenceList.value = [];
    sequenceListIndex.value = -1;
    state.value = 'checking';
    nextState.value = 'checking';
    restartCurrentProgress();
    logs.value = [];
  }

  return {
    state,
    nextState,
    actionProgress,
    logs,

    actionList,
    currentAction,
    shouldSaveSnapshot,
    actionIconSetName,
    actionListIndex,
    actionDescription,
    actionInstruction,
    numActionsToShow,
    enableIdleOnly,
    enableFaceSize,

    sequenceList,
    sequenceListIndex,

    loadActionList,
    restartCurrentProgress,
    restartFaceAction,
    resetFaceAction,
    getNextAction,
    checkAction,
    setToNotPassState,
  };
};

export default { useFaceAction };
