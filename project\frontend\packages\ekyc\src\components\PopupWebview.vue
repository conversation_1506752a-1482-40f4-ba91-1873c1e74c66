<template>
  <div v-if="showPopupWebview" class="popup-top">
    <div class="card">
      <header class="card-header">
        <div class="card-header-title">
          <div class="brand-logo" />
        </div>
      </header>
      <div class="card-content">
        <div class="content">
          <div class="content-text">
            <p class="content-text-logo">
              <ReactiveIcon :icon="targetBrowserLogo" />
            </p>
            <p class="content-text-header">
              {{ $t('PopupWebview.please_open_browser', { browser: targetBrowserName }) }}
            </p>
            <p class="content-text-subheader">
              {{ $t('PopupWebview.subtitle') }}
            </p>
          </div>

          <template v-if="osType === 'android'">
            <go-to-external-button />
          </template>

          <template v-else-if="osType === 'ios'">
            <go-to-safari-button />
          </template>

          <template v-else>
            <!-- This shouldn't been shown -->
          </template>
        </div>
      </div>
    </div>
    <!-- <a
      class="close-modal"
      aria-label="more options"
      @click.prevent="closePopup">
      <span
        class="mdi mdi-close-circle-outline"
        aria-hidden="true" />
      {{ $t('PopupWebview.close_button') }}
    </a> -->
    <div class="popup-top-overlay" />
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import GoToExternalButton from '@helpers/components/GoToExternalButton.vue';
import GoToSafariButton from '@helpers/components/GoToSafariButton.vue';
import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { checkBrowser } from '@helpers/helpers/user-agent';

import { sendEkycLog } from '@ekyc/services/ekyc';

import { useEkycSettings } from '../composables/use-ekyc-settings';
import { usePopup } from '../composables/use-popup';

const props = defineProps({
  media: {
    type: String,
    default: undefined,
  },
});

const { showPopupWebview, showPopupCameraPermission } = usePopup();
const { t } = useI18n();
const { allSettings } = useEkycSettings();
const { osType, badBrowser, isIOS } = checkBrowser();
// const [, scheme, baseUrl] = window.location.href?.match(/^(https?):\/\/(.+)$/) || [];
// eslint-disable-next-line no-nested-ternary
const targetBrowserName = isIOS
  ? t('PopupWebview.browser.safari')
  : t('PopupWebview.browser.chrome');
const targetBrowserLogo = isIOS ? 'logos:safari' : 'logos:chrome';

function closePopup() {
  showPopupWebview.value = false;
  showPopupCameraPermission.value = false;
  const errorBody = {
    media: props.media,
    code: 'close_webview_popup',
    error: '',
    log_type: 'info',
  };
  sendEkycLog(errorBody).catch(() => {});
}
</script>

<style lang="scss" scoped>
.popup-top {
  .card {
    height: unset;
  }
}
</style>
