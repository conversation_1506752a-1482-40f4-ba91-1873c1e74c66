export const EKYC_DOCUMENT_API_TYPES = [
  'front_card',
  'passport',
  'driver_license',
  'residence_permit',
  'thai_alien_card',
  'portrait',
  'ci_passport',
  'work_permit_card',
  'work_permit_book',
  'travel_document',
  'white_card',
  'border_pass',
  'monk_card',
  'immigration_card',
  'other_document',
] as const;

export const EKYC_DOCUMENT_TYPE_CHOICES: {
  value: Types.EkycDocumentItemTypes;
  label: string;
}[] = [
  {
    value: 'front_card',
    label: 'ID Card',
  },
  {
    value: 'passport',
    label: 'Passport',
  },
  {
    value: 'driver_license',
    label: 'Driver License',
  },
  {
    value: 'residence_permit',
    label: 'Residence Permit',
  },
  {
    value: 'thai_alien_card',
    label: 'Thai Alien Card',
  },
  {
    value: 'ci_passport',
    label: 'CI Passport',
  },
  {
    value: 'work_permit_card',
    label: 'Work Permit Card',
  },
  {
    value: 'work_permit_book',
    label: 'Work Permit Book',
  },
  {
    value: 'travel_document',
    label: 'Travel Document',
  },
  {
    value: 'white_card',
    label: 'White Card',
  },
  {
    value: 'border_pass',
    label: 'Border Pass',
  },
  {
    value: 'monk_card',
    label: 'Monk Card',
  },
  {
    value: 'immigration_card',
    label: 'Immigration Card',
  },
  {
    value: 'other_document',
    label: 'Other Document',
  },
] as const;

export const EkycErrorImageNameMap: Record<Types.EkycItemTypes | Types.EkycMediaTypes, string> = {
  liveness: 'id-card-error',
  document: 'id-card-error',
  front_card: 'id-card-error',
  passport: 'passport-error',
  driver_license: 'driver-license-error',
  residence_permit: 'residence-permit-error',
  thai_alien_card: 'thai-alien-card-error',
  portrait: 'id-card-error',
  ci_passport: 'ci-passport-error',
  work_permit_card: 'work-permit-card-error',
  work_permit_book: 'work-permit-book-error',
  travel_document: 'travel-document-error',
  white_card: 'white-card-error',
  border_pass: 'border-pass-error',
  monk_card: 'monk-card-error',
  immigration_card: 'immigration-card-error',
  other_document: 'other-document-error',
  backcard: 'id-card-error',
} as const;

export const EkycFailImageNameMap: Record<Types.EkycItemTypes | Types.EkycMediaTypes, string> = {
  liveness: 'selfie-alert-illustration',
  document: 'id-card-alert-illustration',
  front_card: 'id-card-alert-illustration',
  passport: 'passport-alert-illustration',
  driver_license: 'driver-license-alert-illustration',
  residence_permit: 'residence-permit-alert-illustration',
  thai_alien_card: 'alien-card-alert-illustration',
  portrait: 'id-card-alert-illustration',
  ci_passport: 'ci-passport-alert-illustration',
  work_permit_card: 'work-permit-card-alert-illustration',
  work_permit_book: 'work-permit-book-alert-illustration',
  travel_document: 'travel-document-alert-illustration',
  white_card: 'white-card-alert-illustration',
  border_pass: 'border-pass-alert-illustration',
  monk_card: 'monk-card-alert-illustration',
  immigration_card: 'immigration-card-alert-illustration',
  other_document: 'other-document-alert-illustration',
  backcard: 'id-card-alert-illustration',
} as const;

export const DOCUMENT_BUILDER_ICON_MAP: Record<Types.EkycDocumentItemTypes, string> = {
  front_card: 'id-card',
  passport: 'passport',
  residence_permit: 'residence-permit',
  driver_license: 'driver-license',
  thai_alien_card: 'thai-alien-card',
  portrait: 'portrait',
  ci_passport: 'ci-passport',
  work_permit_card: 'work-permit-card',
  work_permit_book: 'work-permit-book',
  travel_document: 'travel-document',
  white_card: 'white-card',
  border_pass: 'border-pass',
  monk_card: 'monk-card',
  immigration_card: 'immigration-card',
  other_document: 'other-document',
  backcard: 'backcard',
};

export const DOCUMENT_FORM_ICON_MAP: Record<Types.EkycDocumentItemTypes, string> = {
  front_card: 'id-card',
  passport: 'passport',
  residence_permit: 'residence-permit',
  driver_license: 'driver-license',
  thai_alien_card: 'thai-alien-card',
  ci_passport: 'ci-passport',
  portrait: 'portrait',
  work_permit_card: 'work-permit-card',
  work_permit_book: 'work-permit-book',
  travel_document: 'travel-document',
  white_card: 'white-card',
  border_pass: 'border-pass',
  monk_card: 'monk-card',
  immigration_card: 'immigration-card',
  other_document: 'other-document',
  backcard: 'backcard',
};
