/* eslint-disable camelcase */

declare module Types {
  type DEEntity = 'app.form' | 'app.navbar' | 'form.store' | string;
  type DEEvent = 'mounted' | string;

  type DECondition = {
    entity?: DEEntity;
    event?: DEEvent;

    subconditions?: DECondition[];
    operation?: string;

    uid?: string;
  };

  type DEGuardDataType = string | number | boolean | string[] | number[] | boolean[];
  type DEGuardDataTypeAsString = 'string' | 'number' | 'boolean' | 'date' | null;
  type DEGuardDataCategory = 'answer' | DEGuardDataTypeAsString;
  type DEGuardOp =
    | '=='
    | '!='
    | '<'
    | '>'
    | '<='
    | '>='
    | 'empty'
    | 'filled'
    | 'has'
    | 'in'
    | 'starts_with'
    | 'ends_with'
    | 'not_has'
    | 'not_in'
    | 'is'
    | 'is_not';

  type DEGuardDataObject = {
    category: DEGuardDataCategory;
    value: DEGuardDataType;
    value_type: DEGuardDataTypeAsString;
  };

  type DEGuardConditionItem = {
    op: DEGuardOp;
    initial_data: DEGuardDataObject;
    expected_data: DEGuardDataObject;

    uid?: string;
  };

  type DEGuardGroup = {
    children: DEGuardConditionItem[];
    link_operation: 'or' | 'and'; // ['or', 'and', ...]
  };

  type DEGuard = {
    formula?: string;
    groups?: DEGuardGroup[];
    not?: boolean;
  };

  type DEAction = {
    script?: string;
    entity?: DEEntity;
    method: string;
    delay?: number;
    is_async?: boolean;
    params: any;
    skip_params_formula_run?: boolean;
    skip_params_formula_final_run?: boolean;
    chained_params?: {
      enabled?: boolean;
      targets?: {
        action_index?: number;
        path_src?: string | string[];
        path_dest?: string | string[];
        do_merge?: boolean;
      }[];
    };

    uid?: string;
  };

  type DynamicEvent = {
    conditions?: DECondition[];
    guards?: DEGuard[];
    actions: DEAction[];

    runned?: boolean;
    disabled?: boolean;

    uid?: string;
  };
}
