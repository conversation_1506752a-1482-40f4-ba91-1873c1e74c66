<script setup lang="ts">
import { promiseTimeout, templateRef, useElementSize, useSessionStorage } from '@vueuse/core';
import pick from 'lodash/pick';
import { useI18n } from 'vue-i18n-composable';

import ImageSelector from '@helpers/components/ImageSelector.vue';
import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { getResizedResolution } from '@helpers/helpers/image-utils';
import { isPocketDevice } from '@helpers/helpers/user-agent';

import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useCamera } from '@ekyc/composables/use-camera';
import { useConfirmCancel } from '@ekyc/composables/use-confirm-cancel';
import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { getFrameBlob } from '@ekyc/helpers/image-utils';
import { checkQuality } from '@ekyc/services/ekyc';

import ConfirmCancelModal from '../ConfirmCancelModal.vue';
import DebugCameraOverlay from '../DebugCameraOverlay.vue';
import MovingPixel from '../MovingPixel.vue';

const props = defineProps({
  media: {
    type: String as () => 'document' | 'backcard',
    required: true,
    default: 'document',
  },
  itemType: {
    type: String,
    required: true,
    default: 'not_set',
  },
  cancelText: {
    type: String,
    default: 'Cancel ?',
  },
  overlayImage: {
    type: String,
    default: '',
  },
});

const emit = defineEmits<(e: 'finish', val: object) => void>();

const workingConstraints = useSessionStorage('uppass-media-picture-constraints', {});

const { allSettings } = useEkycSettings();
const { t } = useI18n();
const { clickDebug, clickDebugCounter, debugMode, debugOverlay } = useDebug();

function finish(payload: object) {
  emit('finish', payload);
}
const videoRef = templateRef('videoRef');

const requestCameraLog = ref({});
const recordedData = ref<Types.RecordedData>(null);
const verifyingStage = ref(0);
const isShowingConfirm = ref<boolean>(false);
const previewUrl = ref<string>('');

const cardFrameRef = templateRef('cardFrameRef');
const cardFrameRect = reactive({
  camera: {
    x: null,
    y: null,
    width: null,
    height: null,
    video_width: null,
    video_height: null,
    image_width: null,
    image_height: null,
    scale_x: null,
    scale_y: null,
  },
  frame: {
    x: null,
    y: null,
    width: null,
    height: null,
  },
  card: {
    x: null,
    y: null,
    width: null,
    height: null,
  },
});

const preCheckError = ref([]);

const errorContents = reactive({
  blur_detection: {
    title: computed(() => t('ekyc.recorder.error_quality_blur_detection')),
    desc: computed(() => t('ekyc.recorder.error_quality_blur_detection_desc')),
  },
  glare_detection: {
    title: computed(() => t('ekyc.recorder.error_quality_glare_detection')),
    desc: computed(() => t('ekyc.recorder.error_quality_glare_detection_desc')),
  },
  crop: {
    title: computed(() => t('ekyc.recorder.error_quality_crop')),
    desc: computed(() => t('ekyc.recorder.error_quality_crop_desc')),
  },
  brightness: {
    title: computed(() => t('ekyc.recorder.error_quality_brightness')),
    desc: computed(() => t('ekyc.recorder.error_quality_brightness_desc')),
  },
  default: {
    title: computed(() => t('ekyc.recorder.error_quality_default')),
    desc: computed(() => t('ekyc.recorder.error_quality_default_desc')),
  },
});

const hasPreCheckError = computed(() => preCheckError.value.length !== 0);

const noticeDescriptionText = computed(() =>
  hasPreCheckError.value ? preCheckError.value[0]?.desc : errorContents.default.desc,
);

const confirmStepTitleText = computed(() =>
  hasPreCheckError.value ? preCheckError.value[0]?.title : errorContents.default.title,
);

const verifyingText = computed(() => {
  if (verifyingStage.value === 1) {
    return t('ekyc.recorder.checking_quality');
  }
  if (verifyingStage.value === 3) {
    return t('ekyc.recorder.verifying_card_2');
  }
  return t('ekyc.recorder.verifying_card');
});

const captureBtnSrc = computed(() => 'https://cdn.uppass.io/ekyc/assets/common/capture-btn.svg');
const lastImageToShow = computed<string>(() => previewUrl.value || recordedData.value?.url);

const isVertical = computed(() => allSettings[props.media].configs?.enabled_vertical_experience);
const checkQualityUrl = computed(() => allSettings[props.media].check_quality_url);

// Composes
const {
  facingMode,
  isFlipped,
  streamLoaded,
  isSwitchCameraSupported,
  deviceCapabilities,
  deviceSettings,
  mediaStream,
  applyConstraints,
  switchCameraSide,
  loadCamera,
  stopPreview,
  initOnResizeWatch,
} = useCamera({
  videoRef,
  highQuality: false,
  startFacingMode: isPocketDevice() ? 'environment' : 'user',
  overrideConstraints: computed(() => allSettings[props.media].additional_constraints),
  workingConstraints,
});

const { isConfirmCancelModalActive, onClickModalAction } = useConfirmCancel(() =>
  finish({ action: 'close' }),
);

const isShowingActionOverlay = computed<boolean>(
  () => streamLoaded.value && !(verifyingStage.value > 0 || isShowingConfirm.value),
);

const isShowingFlipButton = computed<boolean>(
  () => allSettings[props.media].show_flip_button && isSwitchCameraSupported.value,
);

const overrideProcessingImage = computed(() => allSettings[props.media].override_images.processing);

const IMG_RESIZE_OPTIONS = { maxWidth: 1080, minWidth: 1080 };

async function getFrameBlobWithCurrentSettings() {
  return getFrameBlob(videoRef.value, IMG_RESIZE_OPTIONS);
}

const refreshCardFrameRect = () => {
  const frameRect = cardFrameRef.value?.getBoundingClientRect();
  if (frameRect) {
    cardFrameRect.frame = pick(frameRect, ['x', 'y', 'width', 'height']);
  }

  const videoRect = videoRef.value?.getBoundingClientRect();
  if (videoRect) {
    const { resizedWidth, resizedHeight } = getResizedResolution(
      { inputHeight: videoRef.value.videoHeight, inputWidth: videoRef.value.videoWidth },
      IMG_RESIZE_OPTIONS,
    );

    cardFrameRect.camera = {
      ...pick(videoRect, ['x', 'y', 'width', 'height']),
      video_width: videoRef.value.videoWidth,
      video_height: videoRef.value.videoHeight,
      image_width: resizedWidth,
      image_height: resizedHeight,
      scale_x: resizedWidth / videoRect.width,
      scale_y: resizedHeight / videoRect.height,
    };
  }

  if (!videoRect || !frameRect) {
    console.log('Rect not found, skip refreshCardFrameRect');
    return;
  }

  const cardRect = {
    x: (frameRect.x - videoRect.x) * cardFrameRect.camera.scale_x,
    y: (frameRect.y - videoRect.y) * cardFrameRect.camera.scale_y,
    width: frameRect.width * cardFrameRect.camera.scale_x,
    height: frameRect.height * cardFrameRect.camera.scale_y,
  };
  cardFrameRect.card = cardRect;
  console.log('cardFrameRect:', cardFrameRect);
};

async function takePicture() {
  verifyingStage.value = 1;
  preCheckError.value = [];
  recordedData.value = await getFrameBlobWithCurrentSettings();

  refreshCardFrameRect();

  if (!isVertical.value) {
    confirmImage();
    return;
  }
  if (checkQualityUrl.value && typeof checkQualityUrl.value === 'string') {
    const response = await checkQuality({
      recordedData: recordedData.value,
      url: checkQualityUrl.value,
    });
    previewUrl.value = response.data?.preview_url || '';
    const results: Record<string, { message: string; status: boolean }> =
      response.data?.result || {};
    Object.entries(results).forEach(([key, value]) => {
      if (!value.status) {
        preCheckError.value.push(errorContents[key]);
      }
    });
  }

  verifyingStage.value = 0;
  isShowingConfirm.value = true;
}

function confirmImage() {
  isShowingConfirm.value = false;
  verifyingStage.value = 2;

  finish({
    action: 'complete',
    data: [
      {
        ...recordedData.value,
        filename: 'card.jpg',
      },
    ],
    facingMode: facingMode.value,
    flipped: isFlipped.value,
    logs: [
      {
        request_camera: requestCameraLog.value,
        camera_rect: cardFrameRect.camera,
        frame_rect: cardFrameRect.frame,
        card_rect: cardFrameRect.card,
      },
    ],
  });

  // If waiting for too long, change the message
  const waitingPromise = async () => {
    await promiseTimeout(5000);
    verifyingStage.value = 3;
  };
  waitingPromise();
}

function finishWithCameraError() {
  finish({
    action: 'error',
    code: 'error_open_camera',
    status: 'error_open_camera',
    error: 'ekyc.recorder.error_open_camera',
    error_type: 'error_open_camera',
    message: 'error_open_camera',
    logs: [{ request_camera: requestCameraLog.value }],
  });
}

async function doSwitchCameraSide() {
  const { success, logs } = await switchCameraSide();
  requestCameraLog.value = logs;

  if (!success) {
    finishWithCameraError();
  }
}

async function initFunction() {
  const { width, height } = useElementSize(cardFrameRef);
  initOnResizeWatch(width, height);
  const { success, logs } = await loadCamera();

  requestCameraLog.value = logs;
  if (!success) {
    finishWithCameraError();
  }
}

onMounted(() => {
  initFunction();
});

onBeforeUnmount(() => {
  stopPreview();
});

defineExpose({
  verifyingStage,
  refreshCardFrameRect,
});
</script>

<template>
  <div :class="`ekyc__modal-wrapper-${itemType}`" @click="clickDebug">
    <div class="modal is-active modal-full-screen mask-modal">
      <div class="modal-background" />
      <div
        class="modal-content modal-card camera-view mode-id-card"
        :class="{ 'vertical-experience-enabled': isVertical }"
      >
        <header class="modal-card-head" :class="{ 'modal-card-confirm-head': isVertical }">
          <a
            v-if="isShowingFlipButton && verifyingStage === 0 && !isVertical"
            id="btn-switchcamera"
            class="has-text-light"
            @click="doSwitchCameraSide()"
          >
            <img
              src="https://cdn.uppass.io/ekyc/assets/common/flip-camera_btn.svg"
              alt="Flip camera"
            />
          </a>
          <a
            v-if="isShowingActionOverlay || isShowingConfirm"
            class="mdi mdi-close"
            :class="{ 'close-confirm-btn': isVertical }"
            aria-label="close"
            @click="isConfirmCancelModalActive = true"
          />
        </header>
        <section class="modal-card-body">
          <div class="canvas-wrapper h-full" :class="{ 'image-preview': isShowingConfirm }">
            <!-- Camera -->
            <template v-if="isVertical">
              <div
                v-show="!isShowingConfirm"
                class="layout-control flex flex-col justify-around h-full"
              >
                <div class="font-extrabold text-2xl">{{ t(`ekyc.${media}.header`) }}</div>

                <div class="camera-view-wrapper">
                  <div ref="cardFrameRef" class="camera-view-mask">
                    <video
                      id="camera"
                      ref="videoRef"
                      muted
                      playsinline
                      preload="auto"
                      poster="https://cdn.uppass.io/images/transparent.png"
                    />
                  </div>
                  <ImageSelector id="camera-view-focus-frame_tl" name="id-focus-frame_tl" />
                  <ImageSelector id="camera-view-focus-frame_bl" name="id-focus-frame_bl" />
                  <ImageSelector id="camera-view-focus-frame_tr" name="id-focus-frame_tr" />
                  <ImageSelector id="camera-view-focus-frame_br" name="id-focus-frame_br" />
                </div>

                <div class="font-extrabold text-2xl">{{ t(`ekyc.${media}.subheader`) }}</div>
              </div>

              <!-- Recheck image -->
              <div
                v-if="isShowingConfirm"
                class="layout-control flex flex-col justify-around h-full"
              >
                <div class="font-extrabold text-2xl" :class="{ 'error-title': hasPreCheckError }">
                  <span>{{ confirmStepTitleText }}</span>
                </div>

                <div class="preview-image-wrapper">
                  <img
                    :src="lastImageToShow"
                    alt="taken document"
                    height="300"
                    class="preview-image"
                  />
                </div>

                <a
                  id="btn-retake"
                  @click="isShowingConfirm = false"
                  class="flex items-center justify-center gap-2 text-[var(--app-primary-color)]"
                >
                  <ReactiveIcon icon="mdi:camera-outline" class="text-2xl" />
                  <span class="text-sm font-bold">{{ t(`ekyc.${media}.retake`) }}</span>
                </a>
              </div>
            </template>
            <template v-else>
              <div class="layout-control">
                <div class="ekyc__id-card-mask">
                  <img
                    v-show="mediaStream && verifyingStage === 0"
                    :class="{ flipped: isFlipped }"
                    :src="overlayImage"
                    class="ekyc__id-card-mask-image"
                    alt="Mask"
                  />
                  <video
                    id="camera"
                    ref="videoRef"
                    muted
                    playsinline
                    preload="auto"
                    poster="https://cdn.uppass.io/images/transparent.png"
                  />
                </div>
              </div>

              <template v-if="isShowingActionOverlay">
                <span v-if="t(`ekyc.${itemType}.modal_instruction`)" id="ekyc-modal-instruction">{{
                  t(`ekyc.${itemType}.modal_instruction`)
                }}</span>
                <span v-if="t(`ekyc.${itemType}.frame_instruction`)" id="ekyc-frame-instruction">
                  <span class="mdi mdi-subdirectory-arrow-right mdi-rotate-90 mr-2" />
                  <span>
                    {{ t(`ekyc.${itemType}.frame_instruction`) }}
                  </span>
                </span>
              </template>
            </template>

            <div v-if="isShowingActionOverlay" id="camera-action-panel">
              <div v-if="!isVertical" class="id-capture-alert-message">
                <div class="id-capture-alert-message__wrapper">
                  <div class="id-capture-alert-message__content">
                    <ReactiveIcon
                      icon="lucide:info"
                      class="id-capture-alert-message__icon"
                    ></ReactiveIcon>
                    <span>
                      {{ t(`ekyc.${media}.capture_note`) }}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <div id="capture-button">
                  <span id="id-capture-instruction-message">
                    <span class="instruction-text mr-1">
                      {{ t(`ekyc.${media}.capture_here`) }}
                    </span>
                    <span class="mdi mdi-arrow-right" />
                  </span>
                  <a id="btn-start" @click="takePicture()">
                    <span class="btn-start-text">{{ t(`ekyc.${media}.capture`) }}</span>
                    <img class="btn-start-image" :src="captureBtnSrc" alt="Capture" />
                  </a>
                </div>
              </div>
            </div>
            <div v-if="isShowingConfirm" id="camera-action-panel">
              <div>
                <div id="confirm-section-action">
                  <div></div>
                  <a
                    id="btn-confirm"
                    class="button is-primary"
                    :disabled="hasPreCheckError"
                    @click="confirmImage()"
                  >
                    <span class="btn-start-text">{{ t(`ekyc.${media}.confirm`) }}</span>
                  </a>
                </div>
              </div>
            </div>

            <div v-if="verifyingStage > 0" class="image-capture-preview-wrapper">
              <img :src="lastImageToShow" class="image-capture-preview-item" alt="Preview" />
              <div
                class="camera-processing-overlay"
                :class="{ 'camera-processing-overlay-light': isVertical }"
              >
                <div
                  class="ekyc-action-overlay ekyc-action-result"
                  :class="{ 'ekyc-action-result-light': isVertical }"
                >
                  <div v-if="overrideProcessingImage" class="processing-image-wrapper">
                    <img class="processing-image" :src="overrideProcessingImage" alt="Processing" />
                  </div>
                  <div v-else class="loader" style="font-size: 50px" />
                  <div class="title is-spaced">
                    {{ verifyingText }}
                  </div>
                  <div class="subtitle">
                    {{ t('ekyc.recorder.please_wait') }}
                  </div>
                </div>
              </div>
            </div>

            <div v-if="clickDebugCounter >= 7">
              {{ 10 - clickDebugCounter }}
            </div>
          </div>
        </section>
      </div>
    </div>

    <MovingPixel />

    <ConfirmCancelModal
      :question="cancelText"
      :is-active="isConfirmCancelModalActive"
      @yes="() => onClickModalAction(true)"
      @no="() => onClickModalAction(false)"
    />

    <DebugCameraOverlay
      v-if="debugMode"
      ref="debugOverlay"
      :capabilities="deviceCapabilities"
      :settings="deviceSettings"
      :card-rect="cardFrameRect"
      @apply="applyConstraints"
    >
      <template #top>
        <button class="bg-blue-300" @click="refreshCardFrameRect">refreshCardFrameRect</button>
      </template>
    </DebugCameraOverlay>
  </div>
</template>

<style lang="scss" scoped>
.ekyc__id-card-mask-image.flipped {
  transform: rotateY(180deg) translateY(-6vh) !important;
}
#ekyc-modal-instruction {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 30px;

  z-index: 10;
  color: white;
  top: 30px;
  position: absolute;
  text-align: center;
}
#confirm-section-action {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  position: relative !important;
  bottom: 0 !important;
}

#camera {
  max-width: unset !important;
  max-height: unset !important;
  object-fit: contain !important;
}

#camera-action-panel {
  border-top: solid 0.5px #dee7e7;
  padding: 0.75rem;

  .id-capture-alert-message__wrapper
    .id-capture-alert-message__content
    .id-capture-alert-message__icon {
    color: rgb(42, 48, 73);
    margin-right: 8px;
    width: 1em;
    height: 1em;
    font-size: 16px;
  }

  #confirm-section-action {
    a#btn-retake {
      display: flex;
      height: var(--button-height);
      align-items: center;
      padding: 12px 4px;
      border-radius: var(--app-button-border-radius);
      min-width: 150px;
      max-width: 350px;
      background-color: transparent;
      color: var(--app-button-background-color);
      .btn-start-text {
        font-size: 16px;
        color: inherit;
        font-weight: 700;
      }
    }
    a#btn-confirm {
      display: flex;
      height: var(--button-height);
      align-items: center;
      padding: 12px 24px;
      border-radius: var(--app-button-border-radius);
      min-width: 150px;
      max-width: 350px;
      justify-content: center;
      background-color: var(--app-button-background-color);
      color: var(--app-button-text-color);
      .btn-start-text {
        font-size: 16px;
        color: inherit;
        font-weight: 700;
      }
    }
  }
}

.modal-card-confirm-head {
  justify-content: right !important;
}

.close-confirm-btn {
  color: #777b8b !important;
}

.empty-block {
  height: 240px;
  width: 100%;
  max-width: 500px;
  background-color: transparent;
  margin-bottom: 80px;
}

.camera-processing-overlay-light {
  background-color: white !important;
  .title {
    color: #2a3049 !important;
  }
  .subtitle {
    color: #777b8b !important;
  }
}

.ekyc-main .camera-processing-overlay .ekyc-action-result-light {
  .title {
    color: #2a3049 !important;
    margin-top: 2rem;
  }
  .subtitle {
    color: #777b8b !important;
  }
}

.preview-image-wrapper {
  border-radius: 8px;
  height: 260px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.preview-image {
  height: inherit;
  width: inherit;
  object-fit: cover;
}
</style>
