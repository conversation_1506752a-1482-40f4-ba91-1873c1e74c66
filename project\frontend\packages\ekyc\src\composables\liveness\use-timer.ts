import { useRafFn } from '@vueuse/core';

import { useEkycSettings } from '../use-ekyc-settings';
import { useDebug } from './use-debug';
import { useFaceAction } from './use-face-action';

const recordTimeLeft = ref<number>(0);

export const useTimer = ({ precise = true } = {}) => {
  const { allSettings } = useEkycSettings();
  const { currentAction } = useFaceAction();
  const debugLogic = useDebug();

  const timerInterval = ref<NodeJS.Timeout>();
  const firstFramePassed = ref(false);

  const recordTimeMaxTicks = 10;
  const currentRecordTimeMax = computed<number>(
    () => allSettings.liveness.liveness.recordTimeMax[currentAction.value],
  );
  const recordTimeLeftTicks = computed(() =>
    Math.floor((recordTimeLeft.value / currentRecordTimeMax.value) * recordTimeMaxTicks),
  );

  function loadWait() {
    return new Promise(resolve => {
      const waitingInterval = setInterval(() => {
        if (recordTimeLeft.value >= currentRecordTimeMax.value + 5) {
          recordTimeLeft.value = currentRecordTimeMax.value;
          clearInterval(waitingInterval);
          resolve(true);
        } else {
          recordTimeLeft.value += 1;
        }
      }, 100);
    });
  }

  function deductTimeLeft(second: number) {
    if (debugLogic.freezeTime.value !== true) {
      recordTimeLeft.value -= second;
    }
  }

  function resetTimer() {
    console.log('Reset timer:', recordTimeLeft.value, '->', currentRecordTimeMax.value);
    recordTimeLeft.value = currentRecordTimeMax.value;
  }

  function startTimerPeriod() {
    const UPDATE_PERIOD_MS = 1000;
    // Timer loop
    recordTimeLeft.value = currentRecordTimeMax.value;
    clearInterval(timerInterval.value);
    timerInterval.value = setInterval(() => {
      if (debugLogic.freezeTime.value === true) return;
      recordTimeLeft.value -= UPDATE_PERIOD_MS / 1000;
    }, UPDATE_PERIOD_MS);
  }

  function stopTimerPeriod() {
    clearInterval(timerInterval.value);
  }

  const {
    isActive: isActiveTimerPrecise,
    resume: startTimerPrecise,
    pause: stopTimerPrecise,
  } = useRafFn(
    ({ delta }) => {
      // SKIP first frame since delta cant compare
      if (!firstFramePassed.value) {
        firstFramePassed.value = true;
        return;
      }
      // LOOP Normally
      const diffSec = delta / 1000;
      if (diffSec > 0) {
        deductTimeLeft(diffSec);
      }
    },
    { immediate: false },
  );

  function startTimer() {
    if (precise) {
      startTimerPrecise();
    } else {
      startTimerPeriod();
    }
  }

  function stopTimer() {
    if (precise) {
      stopTimerPrecise();
    } else {
      stopTimerPeriod();
    }
  }

  return {
    recordTimeLeft,
    recordTimeMaxTicks,
    currentRecordTimeMax,
    recordTimeLeftTicks,
    loadWait,
    deductTimeLeft,
    resetTimer,
    startTimer,
    stopTimer,

    startTimerPeriod,
    stopTimerPeriod,

    isActiveTimerPrecise,
    startTimerPrecise,
    stopTimerPrecise,
  };
};

export default useTimer;
