import {
  require_baseSet
} from "./chunk-KI7G4NRT.js";
import {
  require_baseGet
} from "./chunk-3TJ7RJWI.js";
import {
  require_castPath
} from "./chunk-RLNEAPPR.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_basePickBy.js
var require_basePickBy = __commonJS({
  "node_modules/lodash/_basePickBy.js"(exports, module) {
    var baseGet = require_baseGet();
    var baseSet = require_baseSet();
    var castPath = require_castPath();
    function basePickBy(object, paths, predicate) {
      var index = -1, length = paths.length, result = {};
      while (++index < length) {
        var path = paths[index], value = baseGet(object, path);
        if (predicate(value, path)) {
          baseSet(result, castPath(path, object), value);
        }
      }
      return result;
    }
    module.exports = basePickBy;
  }
});

export {
  require_basePickBy
};
//# sourceMappingURL=chunk-A43YZANV.js.map
