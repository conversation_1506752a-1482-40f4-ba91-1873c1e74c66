import { FaceLandmarks68 } from '../face-api-proxies/classes/FaceLandmarks68';
import { Point } from '../face-api-proxies/classes/Point';
import { calculateAngleFromThreePoints } from '../point-functions';

// Mesh positions:
// https://user-images.githubusercontent.com/7452527/53465316-4a282000-3a02-11e9-8e85-0006e3100da0.png
const getLEyePoint = (coords: Types.Coords3D) => coords[374];
const getREyePoint = (coords: Types.Coords3D) => coords[145];
const getNoseTipPoint = (coords: Types.Coords3D) => coords[1];
const getMouthTipPoint = (coords: Types.Coords3D) => coords[0];

export function getTrianglePointsFromPoints(points: [number, number][]) {
  // NOTE: Positions are "MIRRORED"
  const lEye = points[45];
  const rEye = points[36];
  const cEye = [(lEye[0] + rEye[0]) / 2, (lEye[1] + rEye[1]) / 2];
  const nose = points[28];
  const mouth = points[62];
  const lEar = points[0];
  const rEar = points[16];
  return {
    lEye,
    rEye,
    cEye,
    nose,
    mouth,
    lEar,
    rEar,
  };
}
export function getTrianglePointsFromLandmarks(lm: FaceLandmarks68) {
  // NOTE: Positions are "MIRRORED"
  const lEye = lm.positions[45];
  const rEye = lm.positions[36];
  const cEye = new Point((lEye.x + rEye.x) / 2, (lEye.y + rEye.y) / 2);
  const nose = lm.positions[28];
  const mouth = lm.positions[62];
  const lEar = lm.positions[0];
  const rEar = lm.positions[16];
  return {
    lEye,
    rEye,
    cEye,
    nose,
    mouth,
    lEar,
    rEar,
  };
}
export function getTrianglePointsFromFaceMeshCoords(coords: Types.Coords3D) {
  const lEye3D = getLEyePoint(coords);
  const rEye3D = getREyePoint(coords);
  const nose3D = getNoseTipPoint(coords);
  const mouth3D = getMouthTipPoint(coords);
  const lEar3D = getMouthTipPoint(coords);
  const rEar3D = getMouthTipPoint(coords);
  const lEye = new Point(lEye3D[0], lEye3D[1]);
  const rEye = new Point(rEye3D[0], rEye3D[1]);
  const nose = new Point(nose3D[0], nose3D[1]);
  const cEye = new Point((lEye.x + rEye.x) / 2, (lEye.y + rEye.y) / 2);
  const mouth = new Point(mouth3D[0], mouth3D[1]);
  const lEar = new Point(lEar3D[0], lEar3D[1]);
  const rEar = new Point(rEar3D[0], rEar3D[1]);
  return {
    lEye,
    rEye,
    cEye,
    nose,
    mouth,
    lEar,
    rEar,
  };
}

export function getTrianglePoints(positions: FaceLandmarks68 | Types.Coords3D) {
  if (!positions) {
    return null;
  }
  if (positions instanceof FaceLandmarks68) {
    return getTrianglePointsFromLandmarks(positions);
  }
  return getTrianglePointsFromFaceMeshCoords(positions);
}

export function getYawAngle(positions: FaceLandmarks68 | Types.Coords3D) {
  if (!positions) {
    return null;
  }
  const { lEye, cEye, rEye, nose } = getTrianglePoints(positions);

  let angle = 0;
  if (nose.x > lEye.x) {
    return 0;
  }
  if (nose.x < rEye.x) {
    return 180;
  }
  angle = calculateAngleFromThreePoints(lEye, cEye, nose);
  return angle;
}

export function getPitchAngle(positions: FaceLandmarks68 | Types.Coords3D) {
  if (!positions) {
    return null;
  }
  // NOTE: Pitch angle from landmarks is not accurate, so we return 90 for now
  return 90;
  // const { lEye, mouth, rEye } = getTrianglePoints(positions);

  // let angle = 0;
  // if (mouth.y < lEye.y) {
  //   return 180;
  // }
  // angle = calculateAngleFromThreePoints(lEye, mouth, rEye);
  // return angle;
}
