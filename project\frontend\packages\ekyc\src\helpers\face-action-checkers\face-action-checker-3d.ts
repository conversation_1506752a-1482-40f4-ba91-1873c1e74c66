import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';

import { getPitchAngle, getYawAngle } from './utils';

type FaceActionCheckerResult = {
  passed: boolean;
  progress: number;
  log: Types.MethodLog;
};
type PossibleAction = Types.FaceActionPossibleValue | Types.ActionSequencePossibleValue;

export function buildThreshold<T extends PossibleAction>(actionName: T) {
  const { allSettings } = useEkycSettings();
  const threshold = allSettings.liveness.liveness.threshold[actionName];

  const minTargetYaw = (() => {
    if ('yaw_min' in threshold) {
      return threshold.yaw_min;
    }
    if ('yaw_range' in threshold) {
      return 90 - threshold.yaw_range;
    }
    return 0;
  })();

  const maxTargetYaw = (() => {
    if ('yaw_max' in threshold) {
      return threshold.yaw_max;
    }
    if ('yaw_range' in threshold) {
      return 90 + threshold.yaw_range;
    }
    return 180;
  })();

  const minTargetPitch = (() => {
    if ('pitch_min' in threshold) {
      return threshold.pitch_min;
    }
    if ('pitch_range' in threshold) {
      return 90 - threshold.pitch_range;
    }
    return 0;
  })();

  const maxTargetPitch = (() => {
    if ('pitch_max' in threshold) {
      return threshold.pitch_max;
    }
    if ('pitch_range' in threshold) {
      return 90 + threshold.pitch_range;
    }
    return 180;
  })();

  return { ...threshold, minTargetYaw, maxTargetYaw, minTargetPitch, maxTargetPitch };
}

/**
 * Calculates the yaw of a face from MediaPipe face landmarks.
 * Yaw is the rotation of the head around the vertical (Y) axis.
 *
 * @param mesh An array of 478 3D landmark points from MediaPipe Face Mesh.
 * @returns The yaw angle in degrees.
 * - A positive value indicates the head is turned to the user's right.
 * - A negative value indicates the head is turned to the user's left.
 * - A value close to 0 indicates the head is looking straight.
 */
function calculateYaw(mesh: Types.Coords3D): number {
  if (!mesh) {
    return null;
  }
  // Define key landmark indices based on the official MediaPipe map
  // Based on https://developers.google.com/static/ml-kit/vision/face-mesh-detection/images/uv_unwrap_full.png
  const noseTipIndex = 1;
  const leftCheekIndex = 234;
  const rightCheekIndex = 454;

  // Get the 3D coordinates of the key landmarks
  const noseTip = mesh[noseTipIndex];
  const leftCheek = mesh[leftCheekIndex];
  const rightCheek = mesh[rightCheekIndex];

  if (!noseTip || !leftCheek || !rightCheek) {
    console.error('One or more key landmarks are missing from the mesh.');
    return 0;
  }

  // Calculate the midpoint between the cheeks
  const cheekMidpoint = {
    x: (leftCheek.x + rightCheek.x) / 2,
    y: (leftCheek.y + rightCheek.y) / 2,
    z: (leftCheek.z + rightCheek.z) / 2,
  };

  // Calculate the vector from the cheek midpoint to the nose tip.
  // This vector approximates the direction the face is pointing.
  const noseVector = {
    x: noseTip.x - cheekMidpoint.x,
    y: noseTip.y - cheekMidpoint.y,
    z: noseTip.z - cheekMidpoint.z,
  };

  // The yaw angle is the angle of the nose vector on the X-Z plane.
  // We use atan2 to get the angle in radians.
  const yawRadians = Math.atan2(noseVector.z, noseVector.x);

  // Convert the angle from radians to degrees (-179 to 180)
  const yawDegrees = yawRadians * (180 / Math.PI);

  // Convert to (Turn Left: 0 -> Turn Right:180)
  const normalizedYaw = (yawDegrees + 180) % 360; // Convert to (0 to 360)

  return normalizedYaw;
}

function calculatePitch(mesh: Types.Coords3D): number {
  if (!mesh) {
    return null;
  }
  // Define key landmark indices based on the official MediaPipe map
  // Based on https://developers.google.com/static/ml-kit/vision/face-mesh-detection/images/uv_unwrap_full.png
  const noseTipIndex = 1;
  const leftCheekIndex = 234;
  const rightCheekIndex = 454;

  // Get the 3D coordinates of the key landmarks
  const noseTip = mesh[noseTipIndex];
  const leftCheek = mesh[leftCheekIndex];
  const rightCheek = mesh[rightCheekIndex];

  if (!noseTip || !leftCheek || !rightCheek) {
    console.error('One or more key landmarks are missing from the mesh.');
    return 0;
  }

  // Calculate the midpoint between the cheeks
  const cheekMidpoint = {
    x: (leftCheek.x + rightCheek.x) / 2,
    y: (leftCheek.y + rightCheek.y) / 2,
    z: (leftCheek.z + rightCheek.z) / 2,
  };

  // Calculate the vector from the cheek midpoint to the nose tip.
  // This vector approximates the direction the face is pointing.
  const noseVector = {
    x: noseTip.x - cheekMidpoint.x,
    y: noseTip.y - cheekMidpoint.y,
    z: noseTip.z - cheekMidpoint.z,
  };

  // The pitch angle is the angle of the nose vector on the Y-Z plane.
  // We use atan2 to get the angle in radians.
  const pitchRadians = Math.atan2(-noseVector.z, noseVector.y);

  // Convert the angle from radians to degrees (-179 to 180)
  const pitchDegrees = pitchRadians * (180 / Math.PI);

  return pitchDegrees;
}

function validateCheckedArray(checkedArray: { passed: boolean; delta: number }[], frames: number) {
  // Find N in YYYN
  const firstIndexToFail = checkedArray.findIndex(e => !e.passed);

  // [YYY]
  const passedArray = checkedArray.slice(0, firstIndexToFail); // we dont +1 here to ignore delta of between first fail and last pass

  const sumDelta = passedArray.reduce((prev, cur) => prev + cur.delta, 0);

  const neededDelta = frames * 100;
  const progressRaw = (sumDelta / neededDelta) * 100;
  const progress = Math.min(100, progressRaw);
  const passed = progress >= 100;

  return {
    passed_frames: passedArray.length,
    sum_delta: sumDelta,
    progress_raw: progressRaw,
    progress,
    passed,
  };
}

function checkAngle(
  detections: Types.FaceDetectionResult[],
  threshold: ReturnType<typeof buildThreshold<'idle'>>,
): FaceActionCheckerResult {
  const log = {} as Types.MethodLog;

  const checkedArray = [...detections].reverse().map(detection => {
    const yaw = detection.mesh ? calculateYaw(detection.mesh) : getYawAngle(detection.landmarks);
    const pitch = detection.mesh
      ? calculatePitch(detection.mesh)
      : getPitchAngle(detection.landmarks);
    const passedYaw =
      detection.detectionPassed && yaw >= threshold.minTargetYaw && yaw <= threshold.maxTargetYaw;
    const passedPitch =
      detection.detectionPassed &&
      pitch >= threshold.minTargetPitch &&
      pitch <= threshold.maxTargetPitch;
    const passed = passedYaw && passedPitch;

    return {
      passed,
      yaw,
      pitch,
      delta: detection.deltaMs,
    };
  });

  const validated = validateCheckedArray(checkedArray, threshold.frames);

  Object.assign(log, { ...checkedArray[0], ...validated });

  return { passed: validated.passed, progress: validated.progress, log };
}

type BlinkState = 'START' | 'FIRST_CLOSE' | 'FIRST_OPEN' | 'SECOND_CLOSE' | 'SUCCESS';
/**
 * Updates the blink state for a single eye based on new detection data.
 * This function encapsulates the state machine logic to avoid duplication.
 *
 * @param currentState - The current state of the eye ('START', 'FIRST_CLOSE', etc.).
 * @param isClosed - Whether the eye is considered closed in the current frame.
 * @param isOpen - Whether the eye is considered open in the current frame.
 * @param deltaMs - The time elapsed since the last frame.
 * @param currentSequenceTimeMs - The time accumulated since the start of the current blink sequence.
 * @param maxDurationMs - The maximum allowed time for the entire sequence.
 * @returns A tuple containing the new state and the updated sequence time.
 */
function updateBlinkState(
  currentState: BlinkState,
  isClosed: boolean,
  deltaMs: number,
  currentSequenceTimeMs: number,
  maxDurationMs: number,
): [BlinkState, number] {
  const newSequenceTimeMs = currentSequenceTimeMs + deltaMs;

  // If a eye stays open, restarts.
  if (currentState === 'START' && !isClosed) {
    return ['START', 0];
  }

  // If a sequence is in progress for too long, restarts.
  if (currentState !== 'START' && newSequenceTimeMs > maxDurationMs) {
    return ['START', 0];
  }

  if (currentState === 'START' && isClosed) {
    return ['FIRST_CLOSE', deltaMs]; // Start the timer.
  }
  if (currentState === 'FIRST_CLOSE' && !isClosed) {
    return ['FIRST_OPEN', newSequenceTimeMs]; // Continue timing.
  }
  if (currentState === 'FIRST_OPEN' && isClosed) {
    return ['SECOND_CLOSE', newSequenceTimeMs]; // Continue timing.
  }
  if (currentState === 'SECOND_CLOSE' && !isClosed) {
    return ['SUCCESS', newSequenceTimeMs]; // Sequence completed.
  }

  return [currentState, newSequenceTimeMs];
}

/**
 * Checks if the user has performed a double blink (close-open-close-open) on the same eye.
 * This version is refactored for clarity and reduced code duplication.
 *
 * @param detections - An array of face detection results, ordered from oldest to newest.
 * @param threshold - An object with score and duration thresholds.
 * @returns A FaceActionCheckerResult object with the outcome.
 */
function checkBlinkTwice(
  detections: Types.FaceDetectionResult[],
  threshold: ReturnType<typeof buildThreshold<'blink_twice'>>,
): FaceActionCheckerResult {
  const log = {} as Types.MethodLog;

  let state: BlinkState = 'START';
  let sequenceTimeMs = 0;

  // Use the provided max duration or default to 5 second.
  const maxDuration = threshold.blink_timeout_ms;

  // Process detections chronologically.
  for (const detection of detections) {
    const lScore = detection.blendshape?.eyeBlinkLeft ?? 0;
    const rScore = detection.blendshape?.eyeBlinkRight ?? 0;

    const isLeftClosed = lScore >= threshold.blink_min_score;
    const isRightClosed = rScore >= threshold.blink_min_score;

    // Update state for each eye using the helper function.
    [state, sequenceTimeMs] = updateBlinkState(
      state,
      isLeftClosed && isRightClosed,
      detection.deltaMs,
      sequenceTimeMs,
      maxDuration,
    );

    // If either eye successfully completes the sequence, we can stop checking.
    if (state === 'SUCCESS') {
      break;
    }
  }

  const passed = state === 'SUCCESS';

  const progress = {
    START: 0,
    FIRST_CLOSE: 25,
    FIRST_OPEN: 50,
    SECOND_CLOSE: 75,
    SUCCESS: 100,
  }[state];

  Object.assign(log, {
    passed,
    progress,
    sequence_duration_ms: sequenceTimeMs,
  });

  return { passed, progress, log };
}

function checkEyeClose(
  detections: Types.FaceDetectionResult[],
  threshold: ReturnType<typeof buildThreshold<'blink_twice'>>,
  neg = false,
): FaceActionCheckerResult {
  const log = {} as Types.MethodLog;

  const latestDetections = [...detections].reverse();
  const checkedArray = latestDetections.map(detection => {
    const lScore = detection.blendshape?.eyeBlinkLeft ?? 0;
    const rScore = detection.blendshape?.eyeBlinkRight ?? 0;
    const passed = lScore >= threshold.blink_min_score || rScore >= threshold.blink_min_score;

    return {
      passed: neg ? !passed : passed,
      delta: detection.deltaMs,
    };
  });

  const validated = validateCheckedArray(checkedArray, threshold.frames);

  Object.assign(log, {
    ...checkedArray[0],
    ...validated,
  });

  return { passed: validated.passed, progress: validated.progress, log };
}

function checkMouthOpen(
  detections: Types.FaceDetectionResult[],
  threshold: ReturnType<typeof buildThreshold<'mouth_open'>>,
  neg = false,
): FaceActionCheckerResult {
  const log = {} as Types.MethodLog;

  let minLScore = Number.POSITIVE_INFINITY;
  let maxLScore = Number.NEGATIVE_INFINITY;
  let minRScore = Number.POSITIVE_INFINITY;
  let maxRScore = Number.NEGATIVE_INFINITY;

  const latestDetections = [...detections].reverse();
  const checkedArray = latestDetections.map(detection => {
    const score = detection.blendshape?.jawOpen ?? 0;
    const passedMin = score >= threshold.mouth_open_min_score;

    const passed = passedMin;

    return {
      passed: neg ? !passed : passed,
      delta: detection.deltaMs,
    };
  });

  const validated = validateCheckedArray(checkedArray, threshold.frames);

  Object.assign(log, {
    ...checkedArray[0],
    ...validated,
    min_l_score: minLScore,
    max_l_score: maxLScore,
    min_r_score: minRScore,
    max_r_score: maxRScore,
  });

  return { passed: validated.passed, progress: validated.progress, log };
}

export function runCheckerByName(
  actionName: PossibleAction,
  checkerName: Types.PossibleChecker,
  detections: Types.FaceDetectionResult[],
): FaceActionCheckerResult {
  const threshold = buildThreshold(actionName);

  if (checkerName === 'angle') {
    return checkAngle(detections, threshold);
  }
  if (checkerName === 'blink_twice') {
    return checkBlinkTwice(detections, threshold);
  }
  if (checkerName === 'eye_open') {
    return checkEyeClose(detections, threshold, true);
  }
  if (checkerName === 'eye_close') {
    return checkEyeClose(detections, threshold);
  }
  if (checkerName === 'mouth_open') {
    return checkMouthOpen(detections, threshold);
  }
  if (checkerName === 'mouth_close') {
    return checkMouthOpen(detections, threshold, true);
  }
  console.warn(`[Liveness] Checker "${checkerName}" not implemented!`);
  return { passed: false, progress: 0, log: {} as any };
}
