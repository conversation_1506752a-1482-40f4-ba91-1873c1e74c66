<template>
  <div class="debug-camera-overlay">
    <!-- Debug info -->
    <div class="debug-top">
      <div v-for="key in ALLOW_LIST" :key="key">
        <!-- Check if the value is an object to determine the input type -->
        <div
          v-if="typeof capabilities[key] === 'object' && 'min' in capabilities[key]"
          class="columns is-mobile"
        >
          <div class="column">
            <span>{{ key }}:</span>
            <span v-if="typeof manualSettings[key] === 'number'">
              v:{{ manualSettings[key].toFixed(2) }}</span
            >
            <span v-if="capabilities[key].min">|m:{{ capabilities[key].min.toFixed(2) }}</span>
            <span v-if="capabilities[key].max">|M:{{ capabilities[key].max.toFixed(2) }}</span>
            <span v-if="capabilities[key].step">|s:{{ capabilities[key].step.toFixed(2) }}</span>
          </div>
          <div class="column">
            <input
              v-model.number="manualSettings[key]"
              type="range"
              list="tickmarks"
              :min="capabilities[key].min"
              :max="capabilities[key].max"
              :step="capabilities[key].step"
            />
            <datalist id="tickmarks">
              <option :value="0" label="0" />
              <option :value="1" label="1" />
            </datalist>
          </div>
        </div>
        <div v-else-if="Array.isArray(capabilities[key])" class="columns is-mobile">
          <div class="column">
            <span>{{ key }}: </span>
          </div>
          <div class="column">
            <select v-model="manualSettings[key]">
              <option v-for="option in capabilities[key]" :key="key + '_' + option">
                {{ option }}
              </option>
            </select>
          </div>
        </div>
        <div v-else-if="typeof capabilities[key] === 'boolean'">
          <span>{{ key }}:</span>
          <input v-model="manualSettings[key]" type="checkbox" />
        </div>
      </div>
      <slot name="top" />
    </div>

    <!-- Card Frame Rect -->
    <svg
      v-if="cardRect.frame"
      :viewBox="`0 0 ${screenWidth} ${screenHeight}`"
      class="debug-card-rect"
    >
      <!-- Rectangle -->
      <rect
        :x="cardRect.frame.x"
        :y="cardRect.frame.y"
        :width="cardRect.frame.width"
        :height="cardRect.frame.height"
      />

      <!-- Position Marker Lines -->
      <line
        :x1="0"
        :y1="cardRect.frame.y"
        :x2="cardRect.frame.x"
        :y2="cardRect.frame.y"
        stroke="gray"
      />
      <line
        :x1="cardRect.frame.x"
        :y1="0"
        :x2="cardRect.frame.x"
        :y2="cardRect.frame.y"
        stroke="gray"
      />

      <!-- Labels -->
      <text
        :x="((cardRect.frame.x + cardRect.frame.width) * 1) / 6"
        :y="cardRect.frame.y + cardRect.frame.height + 20"
      >
        <tspan>
          FRM x: {{ Math.round(cardRect.frame.x) }} | y: {{ Math.round(cardRect.frame.y) }} | w:
          {{ Math.round(cardRect.frame.width) }} | h: {{ Math.round(cardRect.frame.height) }}
        </tspan>
        <tspan dy="1.2em">
          CRD x: {{ Math.round(cardRect.card.x) }} | y: {{ Math.round(cardRect.card.y) }} | w:
          {{ Math.round(cardRect.card.width) }} | h: {{ Math.round(cardRect.card.height) }}
        </tspan>
      </text>
    </svg>

    <div class="debug-rects">
      <svg v-if="cardRect.card" :viewBox="`0 0 200 200`" class="frame-rect">
        <rect
          :x="50 + (cardRect.camera.x / 2_000) * 100"
          :y="10 + (cardRect.camera.y / 2_000) * 100"
          :width="(cardRect.camera.width / 2_000) * 100"
          :height="(cardRect.camera.height / 2_000) * 100"
          stroke="white"
          fill="#fff5"
        />
        <rect
          :x="50 + (cardRect.frame.x / 2_000) * 100"
          :y="10 + (cardRect.frame.y / 2_000) * 100"
          :width="(cardRect.frame.width / 2_000) * 100"
          :height="(cardRect.frame.height / 2_000) * 100"
          stroke="red"
          fill="#f005"
        />
      </svg>
      <svg v-if="cardRect.card" :viewBox="`0 0 200 200`" class="card-rect">
        <image
          v-if="recordedData"
          :href="recordedData.url"
          :x="0"
          :y="0"
          :width="(recordedData.canvas.width / 2_000) * 200"
          :height="(recordedData.canvas.height / 2_000) * 200"
          opacity="0.7"
          preserveAspectRatio="none"
        />
        <rect
          :x="(cardRect.card.x / 2_000) * 200"
          :y="(cardRect.card.y / 2_000) * 200"
          :width="(cardRect.card.width / 2_000) * 200"
          :height="(cardRect.card.height / 2_000) * 200"
          stroke="green"
          fill="#0f05"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core';
import pick from 'lodash/pick';

type MinMaxStep = {
  max: number;
  min: number;
  step?: number;
};

const props = defineProps({
  capabilities: {
    type: Object as () => {
      iso?: MinMaxStep;
      torch?: boolean;
      zoom?: MinMaxStep;
      focusMode?: string[];
      resizeMode?: string[];
      exposureMode?: string[];
      exposureTime?: MinMaxStep;
      focusDistance?: MinMaxStep;
      colorTemperature?: MinMaxStep;
      whiteBalanceMode?: string[];
      exposureCompensation?: MinMaxStep;
      aspectRatio?: MinMaxStep;
      autoGainControl?: boolean[];
      channelCount?: MinMaxStep;
      deviceId?: string;
      displaySurface?: string;
      echoCancellation?: boolean[];
      facingMode?: string[];
      frameRate?: MinMaxStep;
      groupId?: string;
      height?: MinMaxStep;
      noiseSuppression?: boolean[];
      sampleRate?: MinMaxStep;
      sampleSize?: MinMaxStep;
      width?: MinMaxStep;
    },
    default: () => ({}),
  },
  settings: {
    type: Object,
    default: () => ({}),
  },
  recordedData: {
    type: Object as () => Types.RecordedData,
    default: () => ({}),
  },
  cardRect: {
    type: Object as () => {
      camera: {
        x: number;
        y: number;
        width: number;
        height: number;
        video_width: number;
        video_height: number;
      };
      frame: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
      card: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
    },
    default: () => ({}),
  },
});

const emit = defineEmits<(e: 'apply', value: any) => void>();

const { width: screenWidth, height: screenHeight } = useWindowSize();

const ALLOW_LIST = ['focusMode', 'focusDistance', 'zoom', 'exposureMode', 'exposureTime', 'iso'];

const manualSettings = ref({ ...pick(props.settings, ALLOW_LIST) });

watch(
  manualSettings,
  val => {
    emit('apply', { advanced: [val] });
  },
  { deep: true },
);
</script>
<style lang="scss" scoped>
.debug-camera-overlay {
  @apply text-sm text-white;
  background: rgba(255, 255, 255, 0.1);
  margin: 0;
  .column {
    padding: 0;
    line-height: 2rem;
    white-space: nowrap;
  }

  * {
    font-size: x-small;
  }

  input,
  select {
    height: 1rem;
  }
}

.debug-top {
  @apply fixed top-[2%] left-0 z-[89999];
  @apply opacity-10 hover:opacity-100;
}

.debug-card-rect {
  @apply fixed top-0 left-0 z-[89998];
  @apply fill-transparent stroke-red-700;
}

.debug-rects {
  @apply flex gap-1;
  @apply fixed top-0 m-auto z-[89998];
}

.frame-rect {
  @apply w-[200px] h-[200px];
  @apply fill-transparent stroke-yellow-700;
}

.card-rect {
  @apply w-[200px] h-[200px];
  @apply fill-transparent stroke-yellow-700;
}
</style>
