import * as Sentry from '@sentry/vue';

import pinia from '@helpers/plugins/pinia';

export { Sentry };

export function initSentry(
  Vue: import('vue').VueConstructor,
  router: import('vue-router').default,
  getSettings: (key: string, defaultValue?: any) => any,
) {
  if (!getSettings('VUE_APP_SENTRY_ENABLED')) {
    return;
  }

  const dsn = getSettings('VUE_APP_SENTRY_DSN');
  const environment = getSettings('VUE_APP_SENTRY_ENVIRONMENT');
  const sendDefaultPii = getSettings('VUE_APP_SENTRY_SEND_DEFAULT_PII');
  const enablePiniaPlugin = getSettings('VUE_APP_SENTRY_ENABLE_PINIA_PLUGIN');
  const enableRouterTracking = getSettings('VUE_APP_SENTRY_ENABLE_ROUTER_TRACKING');
  const enableComponentTracking = getSettings('VUE_APP_SENTRY_ENABLE_COMPONENT_TRACKING');
  const normalizeDepth = getSettings('VUE_APP_SENTRY_NORMALIZE_DEPTH');
  const tracesSampleRate = getSettings('VUE_APP_SENTRY_TRACES_SAMPLE_RATE');
  const tracesSampleRateOnDashboard = getSettings('VUE_APP_SENTRY_TRACES_SAMPLE_RATE_ON_DASHBOARD');
  const profilesSampleRate = getSettings('VUE_APP_SENTRY_PROFILES_SAMPLE_RATE');
  const replaysSessionSampleRate = getSettings('VUE_APP_SENTRY_REPLAYS_SESSION_SAMPLE_RATE');
  const replaysOnErrorSampleRate = getSettings('VUE_APP_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE');
  const additionalInitOptions = getSettings('VUE_APP_SENTRY_ADDITIONAL_INIT_OPTIONS');

  console.log('Sentry enabled:', {
    environment,
    sendDefaultPii,
    enablePiniaPlugin,
    enableRouterTracking,
    enableComponentTracking,
  });

  Sentry.init({
    Vue,
    dsn: dsn,
    environment: environment,
    integrations: [
      // https://docs.sentry.io/platforms/javascript/guides/vue/tracing/
      Sentry.browserTracingIntegration({
        router: enableRouterTracking ? router : undefined,
        routeLabel: 'path',
      }),
      // https://docs.sentry.io/platforms/javascript/guides/vue/profiling/
      Sentry.browserProfilingIntegration(),
      // https://docs.sentry.io/platforms/javascript/guides/vue/session-replay/
      Sentry.replayIntegration({
        // Additional SDK configuration goes in here, for example:
        maskAllText: true,
        blockAllMedia: true,
      }),
      // https://docs.sentry.io/platforms/javascript/guides/vue/logs/
      Sentry.consoleLoggingIntegration(),
      // https://docs.sentry.io/platforms/javascript/guides/vue/configuration/integrations/captureconsole/
      // Sentry.captureConsoleIntegration(),
      // https://docs.sentry.io/platforms/javascript/guides/vue/configuration/integrations/httpclient/
      Sentry.httpClientIntegration(),
      // https://docs.sentry.io/platforms/javascript/guides/vue/features/component-tracking/
      Sentry.vueIntegration({
        tracingOptions: {
          trackComponents: enableComponentTracking,
        },
      }),
    ],
    // Setting this option to true will send default PII data to Sentry.
    // For example, automatic IP address collection on events
    sendDefaultPii: sendDefaultPii,
    // By default, Sentry SDKs normalize any context to a depth of 3.
    // You may want to increase this (ex: 10) for sending Pinia states by passing normalizeDepth to the Sentry.init call:
    normalizeDepth: normalizeDepth,
    // Tracing; 1.0=Capture 100% of the transactions
    // tracesSampleRate: tracesSampleRate,
    // https://docs.sentry.io/platforms/javascript/guides/vue/tracing/configure-sampling/
    tracesSampler: samplingContext => {
      const { name, inheritOrSampleWith } = samplingContext;
      // Sample from dashboard
      if (name.includes('/dashboard')) {
        return tracesSampleRateOnDashboard;
      }
      // Sample from elsewhere
      return inheritOrSampleWith(tracesSampleRate);
    },
    // Set profilesSampleRate to 1.0 to profile every transaction.
    // Since profilesSampleRate is relative to tracesSampleRate,
    // the final profiling rate can be computed as tracesSampleRate * profilesSampleRate
    // For example, a tracesSampleRate of 0.5 and profilesSampleRate of 0.5 would
    // results in 25% of transactions being profiled (0.5*0.5=0.25)
    profilesSampleRate: profilesSampleRate,
    // Replays
    // This sets the sample rate to be 10%. You may want this to be 100% while
    // in development and sample at a lower rate in production
    replaysSessionSampleRate: replaysSessionSampleRate,
    // If the entire session is not sampled, use the below sample rate to sample
    // sessions when an error occurs.
    replaysOnErrorSampleRate: replaysOnErrorSampleRate,
    // Enable logs to be sent to Sentry
    _experiments: { enableLogs: true },
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: ['localhost', /^https:\/\/.+\.uppass\/io/],
    // A list of strings or regex patterns that match error messages that shouldn't be sent to Sentry.
    // Messages that match these strings or regular expressions will be filtered out before they're sent to Sentry.
    // When using strings, partial matches will be filtered out, so if you need to filter by exact match, use regex patterns instead.
    // By default, all errors are sent.
    // ignoreErrors: [
    //   /^AbortError:/,
    //   /^Failed to fetch dynamically imported module:/,
    //   /^Unable to preload CSS/,
    // ],
    ...additionalInitOptions,
  });

  if (enablePiniaPlugin) {
    pinia.use(Sentry.createSentryPiniaPlugin());
  }
}
