/* eslint-disable camelcase */
/*
 * Calculates the angle ABC (in radians)
 *
 * A first point, ex: {x: 0, y: 0}
 * C second point
 * B center point
 */
import { Point } from './face-api-proxies/classes/Point';

export function calEuclidean(x1: number, y1: number, x2: number, y2: number): number {
  // calculate distance
  const a: number = x1 - x2;
  const b: number = y1 - y2;
  const c: number = Math.sqrt(a * a + b * b);
  return c;
}

export function calEuclidean3D(
  x1: number,
  y1: number,
  z1: number,
  x2: number,
  y2: number,
  z2: number,
): number {
  // calculate distance
  const a: number = x1 - x2;
  const b: number = y1 - y2;
  const c: number = z1 - z2;
  const d: number = Math.sqrt(a * a + b * b + c * c);
  return d;
}

export function getDistance(points: Point[], isClosed: boolean = false): number {
  let distance = 0;
  points.slice(1).forEach((_a, prevIdx) => {
    const from = points[prevIdx];
    distance += calEuclidean(from.x, from.y, _a.x, _a.y);
  });
  // If points is closed graph (mouth ? eyes ?)
  if (isClosed) {
    const from = points[points.length - 1];
    const to = points[0];
    if (!from || !to) {
      return 0;
    }
    distance += calEuclidean(from.x, from.y, to.x, to.y);
  }
  return distance;
}

export function getDistance3D(points: Types.Coords3D, isClosed: boolean = false): number {
  let distance = 0;
  points.slice(1).forEach((_a, prevIdx) => {
    const from = points[prevIdx];
    distance += calEuclidean3D(from[0], from[1], from[2], _a[0], _a[1], _a[2]);
  });
  // If points is closed graph (mouth ? eyes ?)
  if (isClosed) {
    const from = points[points.length - 1];
    const to = points[0];
    if (!from || !to) {
      return 0;
    }
    distance += calEuclidean3D(from[0], from[1], from[2], to[0], to[1], to[2]);
  }
  return distance;
}

export function calculateAngleFromThreePoints(A: Point, B: Point, C: Point) {
  const AB = Math.sqrt((B.x - A.x) ** 2 + (B.y - A.y) ** 2);
  const BC = Math.sqrt((B.x - C.x) ** 2 + (B.y - C.y) ** 2);
  const AC = Math.sqrt((C.x - A.x) ** 2 + (C.y - A.y) ** 2);
  const radians = Math.acos((BC ** 2 + AB ** 2 - AC ** 2) / (2 * BC * AB));
  const degrees = radians * (180 / Math.PI);
  return degrees;
}
