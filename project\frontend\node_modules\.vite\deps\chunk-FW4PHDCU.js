import {
  require_last
} from "./chunk-YXYNTHJR.js";
import {
  require_baseGet
} from "./chunk-3TJ7RJWI.js";
import {
  require_castPath,
  require_toKey
} from "./chunk-RLNEAPPR.js";
import {
  require_baseSlice
} from "./chunk-WI7ETHBW.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_parent.js
var require_parent = __commonJS({
  "node_modules/lodash/_parent.js"(exports, module) {
    var baseGet = require_baseGet();
    var baseSlice = require_baseSlice();
    function parent(object, path) {
      return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
    }
    module.exports = parent;
  }
});

// node_modules/lodash/_baseUnset.js
var require_baseUnset = __commonJS({
  "node_modules/lodash/_baseUnset.js"(exports, module) {
    var castPath = require_castPath();
    var last = require_last();
    var parent = require_parent();
    var toKey = require_toKey();
    function baseUnset(object, path) {
      path = castPath(path, object);
      object = parent(object, path);
      return object == null || delete object[toKey(last(path))];
    }
    module.exports = baseUnset;
  }
});

export {
  require_baseUnset
};
//# sourceMappingURL=chunk-FW4PHDCU.js.map
