import {
  require_getTag
} from "./chunk-BVJZN6TR.js";
import "./chunk-WEVAHQUU.js";
import {
  require_baseKeys
} from "./chunk-D4QEHMHD.js";
import {
  require_isBuffer,
  require_isTypedArray
} from "./chunk-RWM7CMLN.js";
import {
  require_isPrototype
} from "./chunk-GYHWOQRN.js";
import {
  require_isArguments
} from "./chunk-IW7F5ZXM.js";
import "./chunk-F753BK7A.js";
import "./chunk-7I5PEIZF.js";
import {
  require_isArray
} from "./chunk-TP2NNXVG.js";
import {
  require_isArrayLike
} from "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/isEmpty.js
var require_isEmpty = __commonJS({
  "node_modules/lodash/isEmpty.js"(exports, module) {
    var baseKeys = require_baseKeys();
    var getTag = require_getTag();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isArrayLike = require_isArrayLike();
    var isBuffer = require_isBuffer();
    var isPrototype = require_isPrototype();
    var isTypedArray = require_isTypedArray();
    var mapTag = "[object Map]";
    var setTag = "[object Set]";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function isEmpty(value) {
      if (value == null) {
        return true;
      }
      if (isArrayLike(value) && (isArray(value) || typeof value == "string" || typeof value.splice == "function" || isBuffer(value) || isTypedArray(value) || isArguments(value))) {
        return !value.length;
      }
      var tag = getTag(value);
      if (tag == mapTag || tag == setTag) {
        return !value.size;
      }
      if (isPrototype(value)) {
        return !baseKeys(value).length;
      }
      for (var key in value) {
        if (hasOwnProperty.call(value, key)) {
          return false;
        }
      }
      return true;
    }
    module.exports = isEmpty;
  }
});
export default require_isEmpty();
//# sourceMappingURL=lodash_isEmpty.js.map
