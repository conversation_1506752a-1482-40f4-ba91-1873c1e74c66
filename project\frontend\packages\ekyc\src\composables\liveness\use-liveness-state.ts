import throttle from 'lodash/throttle';

import { useFaceAction } from '@ekyc/composables/liveness/use-face-action';
import { FaceLandmarks68 } from '@ekyc/helpers/face-api-proxies/classes/FaceLandmarks68';
import { LivenessLog } from '@ekyc/lib/liveness-log';

// Data
const errorMessages = ref<string[]>([]);
const numActionPassed = ref<number>(0);
/** 1 snapFrameNumber = 1 the snap + face detection run */
const snapFrameNumber = ref<number>(0);
/** 1 actionFrameNumber = 1 snapFrameNumber but reset after each action passed */
const actionFrameNumber = ref<number>(0);
const actionTimeSpent = ref<number>(0);
const isActionPassed = ref<boolean>(false);
const isActionFailed = ref<boolean>(false);
const allLogs = ref<LivenessLog[]>([]);
const livenessTimeStart = ref<Date>();

const idleLandMarks = ref<FaceLandmarks68>();
const idleMesh = ref<Types.Coords3D>();

const recordingState = ref<
  | 'loading'
  | 'pre-recording'
  | 'early-recording'
  | 'recording'
  | 'background-checking'
  | 'uploading'
>('loading');

export const useLivenessState = () => {
  const { currentAction } = useFaceAction();
  const isLoading = computed<boolean>(() => recordingState.value === 'loading');
  const isRecording = computed<boolean>(() =>
    ['early-recording', 'recording', 'background-checking'].includes(recordingState.value),
  );

  const isShowingInstruction = computed<boolean>(() =>
    ['', 'loading', 'pre-recording'].includes(recordingState.value),
  );
  const isShowingActionOverlay = computed<boolean>(
    () =>
      ['', 'loading', 'pre-recording', 'early-recording', 'recording'].includes(
        recordingState.value,
      ) &&
      !isActionPassed.value &&
      !isActionFailed.value,
  );
  const isShowingCloseBtn = computed<boolean>(() => isShowingActionOverlay.value);

  function resetLivenessState() {
    recordingState.value = 'loading';
    idleLandMarks.value = undefined;
    idleMesh.value = undefined;
    errorMessages.value = [];
    numActionPassed.value = 0;
    snapFrameNumber.value = 0;
    actionFrameNumber.value = 0;
    isActionPassed.value = false;
    isActionFailed.value = false;
    allLogs.value = [];
    livenessTimeStart.value = null;
  }

  function addErrorMessage(errMsg: string) {
    if (errorMessages.value.indexOf(errMsg) === -1) {
      errorMessages.value.push(errMsg);
    }
  }

  function createLivenessLog() {
    return new LivenessLog(currentAction.value, snapFrameNumber.value);
  }

  let lastTime: number = null;

  function addLivenessLogs(livenessLog: LivenessLog) {
    // Assign log delta
    lastTime = lastTime ?? Date.now();
    const currentTime = Date.now();
    livenessLog.assignInfo({ log_delta_ms: currentTime - lastTime });
    lastTime = Date.now();

    // Cap the log
    const MAX_LENGTH = 300; // 10 f/s * 10s * 3 actions
    let finalLogs = [...allLogs.value, livenessLog];
    if (finalLogs.length > MAX_LENGTH) {
      const HALF_LENGTH = MAX_LENGTH / 2;
      finalLogs = [...finalLogs.slice(0, HALF_LENGTH), ...finalLogs.slice(-HALF_LENGTH)];
    }
    allLogs.value = finalLogs;
  }

  function assignLatestLivenessLogResult(result: Partial<LivenessLog['result']>) {
    if (!allLogs.value.length) {
      addLivenessLogs(createLivenessLog());
    }
    Object.assign(allLogs.value[allLogs.value.length - 1].result, result);
  }

  return {
    errorMessages,
    numActionPassed,
    snapFrameNumber,
    actionFrameNumber,
    actionTimeSpent,
    isActionPassed,
    isActionFailed,
    allLogs,
    livenessTimeStart,
    idleLandMarks,
    idleMesh,
    recordingState,

    isLoading,
    isRecording,
    isShowingInstruction,
    isShowingCloseBtn,
    isShowingActionOverlay,

    addErrorMessage,
    resetLivenessState,
    createLivenessLog,
    addLivenessLogs: throttle(addLivenessLogs, 100),
    assignLatestLivenessLogResult,
  };
};

export default useLivenessState;
