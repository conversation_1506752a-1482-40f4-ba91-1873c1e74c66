const showPopupIsDesktop = ref<boolean>();
const showPopupWebview = ref<boolean>();
const showPopupCameraPermission = ref<boolean>();
const showPopupCameraLockdown = ref<boolean>();
const showPopupCantLoadModel = ref<boolean>();
const shouldPopupIsDesktopHidden = ref<boolean>();
const showPopupGoToSetting = ref<boolean>();
const overridePopupGoToSettingContent = ref<string>();
const onPopupTryToOpenCamera = ref<Function>(() => {});

export const usePopup = () => {
  function hideAllPopup() {
    showPopupIsDesktop.value = false;
    showPopupWebview.value = false;
    showPopupCameraPermission.value = false;
    showPopupCameraLockdown.value = false;
    showPopupCantLoadModel.value = false;
    showPopupGoToSetting.value = false;
    overridePopupGoToSettingContent.value = '';
  }

  return {
    showPopupIsDesktop,
    showPopupWebview,
    showPopupCameraPermission,
    showPopupCameraLockdown,
    showPopupCantLoadModel,
    showPopupGoToSetting,
    overridePopupGoToSettingContent,

    shouldPopupIsDesktopHidden,

    hideAllPopup,
    onPopupTryToOpenCamera,
  };
};

export default usePopup;
