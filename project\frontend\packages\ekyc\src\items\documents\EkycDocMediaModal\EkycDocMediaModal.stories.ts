import type { <PERSON>a, StoryObj } from '@storybook/vue';

import { setCameraBlockRules } from '../../../composables/use-camera';
import EkycDocMediaModal from './EkycDocMediaModal.vue';

type ArgsType = InstanceType<typeof EkycDocMediaModal>['$props'] & {
  isDisplayMediaRecorder: boolean;
  currentState: 'selector' | 'camera' | 'preview';
  debugMode: boolean;
  startInit: boolean;
};

const meta: Meta<ArgsType> = {
  component: EkycDocMediaModal,
  parameters: {
    viewport: { defaultViewport: 'mobile2' },
  },
  render: ({ isDisplayMediaRecorder, currentState, debugMode, startInit = false, ...args }) => ({
    components: { EkycDocMediaModal },
    setup() {
      const rootRef = ref<InstanceType<typeof EkycDocMediaModal>>();
      setCameraBlockRules({ default: false });

      onMounted(() => {
        if (isDisplayMediaRecorder) {
          rootRef.value.isDisplayMediaRecorder = isDisplayMediaRecorder;
        }

        if (debugMode) {
          rootRef.value.debugMode = debugMode;
        }

        if (currentState) {
          rootRef.value.currentState = currentState;
        }

        if (startInit) {
          rootRef.value.startInit();
        }
      });

      return { args, rootRef };
    },
    template: `<EkycDocMediaModal ref="rootRef" v-bind="args"></EkycDocMediaModal>`,
  }),
  decorators: [
    (_, { args }) => ({
      template: `
        <div class="ekyc-main">
          <div id="main-container">
            <story />
          </div>
        </div>`,
    }),
  ],
};

export default meta;

type Story = StoryObj<ArgsType>;

const BASE: Story = {
  args: {
    media: 'document',
    itemType: 'front_card',
    selectedCountry: 'THA',
    setting: {},
  },
  parameters: {
    docs: {
      story: {
        inline: false,
        height: 650,
        // autoplay: false,
      },
    },
  },
};

export const Primary: Story = {
  ...BASE,
};

export const Selector: Story = {
  ...BASE,
  args: {
    ...BASE.args,
    currentState: 'selector',
    startInit: true,
  },
};

export const Camera: Story = {
  ...BASE,
  args: {
    ...BASE.args,
    currentState: 'camera',
    startInit: true,
  },
};
