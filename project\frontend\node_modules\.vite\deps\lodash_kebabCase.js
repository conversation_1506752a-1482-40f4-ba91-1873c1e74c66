import {
  require_createCompounder
} from "./chunk-T7CKRGHI.js";
import "./chunk-TPPCP22B.js";
import "./chunk-7XLBW5XD.js";
import "./chunk-CWSHORJK.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/kebabCase.js
var require_kebabCase = __commonJS({
  "node_modules/lodash/kebabCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var kebabCase = createCompounder(function(result, word, index) {
      return result + (index ? "-" : "") + word.toLowerCase();
    });
    module.exports = kebabCase;
  }
});
export default require_kebabCase();
//# sourceMappingURL=lodash_kebabCase.js.map
