{"version": 3, "sources": ["../../lodash/_baseSet.js"], "sourcesContent": ["var assignValue = require('./_assignValue'),\n    castPath = require('./_castPath'),\n    isIndex = require('./_isIndex'),\n    isObject = require('./isObject'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nmodule.exports = baseSet;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,UAAU;AAFd,QAGI,WAAW;AAHf,QAII,QAAQ;AAYZ,aAAS,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAChD,UAAI,CAAC,SAAS,MAAM,GAAG;AACrB,eAAO;AAAA,MACT;AACA,aAAO,SAAS,MAAM,MAAM;AAE5B,UAAI,QAAQ,IACR,SAAS,KAAK,QACd,YAAY,SAAS,GACrB,SAAS;AAEb,aAAO,UAAU,QAAQ,EAAE,QAAQ,QAAQ;AACzC,YAAI,MAAM,MAAM,KAAK,KAAK,CAAC,GACvB,WAAW;AAEf,YAAI,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,aAAa;AACvE,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,WAAW;AACtB,cAAI,WAAW,OAAO,GAAG;AACzB,qBAAW,aAAa,WAAW,UAAU,KAAK,MAAM,IAAI;AAC5D,cAAI,aAAa,QAAW;AAC1B,uBAAW,SAAS,QAAQ,IACxB,WACC,QAAQ,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UACxC;AAAA,QACF;AACA,oBAAY,QAAQ,KAAK,QAAQ;AACjC,iBAAS,OAAO,GAAG;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}