import { useEkycSettings } from '../use-ekyc-settings';

const detectionHistory = ref<Types.FaceDetectionResult[]>([]);

export const useHistory = () => {
  const { allSettings } = useEkycSettings();

  const currentHistory = computed<Types.FaceDetectionResult | null>(() =>
    detectionHistory.value.length > 0
      ? (detectionHistory.value.slice(-1)[0] as Types.FaceDetectionResult)
      : null,
  );

  const maxHistorySize = computed((): number => allSettings.liveness.liveness.maxHistorySize); // Size of result we keep to send if failed

  const addDetectionHistory = (newHistory: Types.FaceDetectionResult) => {
    if (detectionHistory.value.length >= maxHistorySize.value) {
      detectionHistory.value = [...detectionHistory.value.slice(1), newHistory];
    } else {
      detectionHistory.value.push(newHistory);
    }
  };

  const clearDetectionHistories = () => {
    detectionHistory.value = [];
  };

  return {
    detectionHistory: readonly(detectionHistory),
    currentHistory,
    addDetectionHistory,
    clearDetectionHistories,
  };
};

export default useHistory;
