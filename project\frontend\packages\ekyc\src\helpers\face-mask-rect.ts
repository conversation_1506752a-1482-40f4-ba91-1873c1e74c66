import type FaceMaskSvg from '@ekyc/components/liveness-modal/FaceMaskSvg.vue';

export function calculateFaceMaskRect(
  cameraInputElement: HTMLVideoElement,
  faceMaskOverlay: InstanceType<typeof FaceMaskSvg>,
): Types.FaceMaskRectInfo {
  let activeRect = { height: 0, width: 0 } as Types.CommonRect;
  let overlayRect = { height: 0, width: 0 } as Types.CommonRect;
  let faceMaskRect = { height: 0, width: 0 } as Types.CommonRect;
  try {
    if (cameraInputElement) {
      activeRect = {
        ...cameraInputElement.getBoundingClientRect().toJSON(),
      };
      activeRect = {
        ...activeRect,
        computed_width: activeRect.width,
        computed_height: activeRect.height,
        width: cameraInputElement.offsetWidth,
        height: cameraInputElement.offsetHeight,
      };
    }

    const faceMaskOverlayElement = faceMaskOverlay.$el;
    if (faceMaskOverlayElement) {
      overlayRect = faceMaskOverlayElement.getBoundingClientRect().toJSON();

      const faceMaskSelector = '#face-mask-rect-face-svg-mask';
      const faceMaskElement: SVGPathElement =
        faceMaskOverlayElement.querySelector(faceMaskSelector);

      if (faceMaskElement) {
        const rect = faceMaskElement.getBoundingClientRect();
        faceMaskRect = {
          height: rect.height,
          width: rect.width,
          left: rect.x,
          top: rect.y,
          right: rect.x + rect.width,
          bottom: rect.y + rect.height,
        };
      }
    }
  } catch (err) {
    console.error(err);
  }

  return {
    camera: {
      width: cameraInputElement.videoWidth,
      height: cameraInputElement.videoHeight,
    },
    active_rect: activeRect,
    overlay_rect: overlayRect,
    face_mask_rect: faceMaskRect,
  };
}

export default { calculateFaceMaskRect };
