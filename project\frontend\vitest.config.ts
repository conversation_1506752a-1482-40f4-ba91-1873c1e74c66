import dsv from '@rollup/plugin-dsv';
import vue from '@vitejs/plugin-vue2';
import { resolve } from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import DefineOptions from 'unplugin-vue-define-options/vite';
import { configDefaults, defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [
    vue(),
    DefineOptions(),
    AutoImport({
      imports: ['vitest', 'vue'],
      dts: false,
    }),
    dsv(),
  ],
  resolve: {
    alias: {
      vue: 'vue/dist/vue.runtime.mjs',
    },
  },
  test: {
    projects: [
      './vitest.config.ts',
      './packages/ekyc/vite.config.mts',
      './packages-other/puppeteer/vite.config.ts',
    ],
    hookTimeout: 30000,
    environment: 'happy-dom',
    deps: {
      optimizer: {
        web: {
          include: ['vitest-canvas-mock'],
        },
      },
    },
    alias: [
      { find: '@', replacement: resolve(__dirname, './src') },
      { find: '@core', replacement: resolve(__dirname, './packages/core/src') },
      { find: '@helpers', replacement: resolve(__dirname, './packages/helpers/src') },
      { find: '@ekyc', replacement: resolve(__dirname, './packages/ekyc/src') },
      {
        find: 'vue-i18n-composable',
        replacement: resolve(__dirname, 'packages/helpers/src/helpers/vue-i18n-composable.ts'),
      },
      { find: '_tests', replacement: resolve(__dirname, './tests') },
      {
        find: 'flow-graph',
        replacement: resolve(__dirname, 'src/composables/decision-flow/flow-graph.mjs'),
      },
    ],
    exclude: [
      ...configDefaults.exclude,
      './tests/e2e/**',
      './tests/unit/__**/**',
      './tests/unit/**/__**',
      './tests/unit/**/__**/**',
      './packages-vue-3/**',
    ],
    setupFiles: ['./tests/unit/setup.ts'],
    globalSetup: ['./tests/unit/global-setup.ts'],
    globals: true,
    coverage: {
      enabled: false, // Only enable in CI/CD
      provider: 'v8',
    },
  },
});
