<script setup lang="ts">
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';

import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import DynamicPageView from './DynamicPageView.vue';

const props = defineProps({
  lang: {
    required: true,
    type: String,
  },
  code: {
    type: String,
    default: '404',
  },
  message: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: 'mdi-block-helper',
  },
});

const { mainInstance } = useDynamicFormApp();
const { t } = useI18n();
const currentUrl = window.location.href;

onMounted(() => {
  mainInstance.value.setFormData({
    field: 'error_icon',
    value: props.icon,
  });
  mainInstance.value.setFormData({
    field: 'error_code',
    value: props.code,
  });
  mainInstance.value.setFormData({
    field: 'error_message',
    value: t(props.message).toString() || t('errors.page_not_found').toString(),
  });
  const messageToParent = JSON.stringify({ type: 'route', data: currentUrl });
  // eslint-disable-next-line no-restricted-globals
  parent.postMessage(messageToParent, window.location.origin);
});
</script>

<template>
  <div class="container has-text-centered centerall">
    <DynamicPageView page-name="error" :lang="lang" />
    <div class="not-found-info">
      <span>{{ currentUrl }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.centerall {
  height: 100vh;
  padding: 0;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  :deep(.ok-dy__dynamic_page) .content #ok-dy__FormItem-image .has-text-primary {
    text-align: center !important;
    text-align: -webkit-center !important;
  }
}
.not-found-info {
  position: absolute;
  bottom: 0;
  color: #fff0;
}
</style>
