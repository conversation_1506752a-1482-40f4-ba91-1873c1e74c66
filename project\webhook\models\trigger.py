import copy
import json
from datetime import datetime

import requests
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from rest_framework import status

from base.cryptography.symmetric import AESCBC

from ..helpers.sjcl_encryption import decrypt, encrypt
from .task import Task


class Trigger(models.Model):
    class Authorization(models.TextChoices):
        NO_AUTH = 'no_auth', _('None')
        BASIC_AUTH = 'basic_auth', _('basic')
        BEARER_TOKEN = 'bearer_token', _('token')
        GCP_SERVICE_TO_SERVICE = 'gcp_service_to_service', _('GCP service-to-service')
        JWT = 'jwt', _('jwt')

    key = settings.SECRET_KEY
    task = None
    webhook = None
    is_mockup = False
    
    class Meta:
        abstract = True

    @property
    def current_detail(self):
        return {}

    @property
    def method_type(self):
        return None

    @property
    def description(self):
        return ''

    @property
    def webhook_name(self):
        try:
            return self.webhook.name if self.webhook else None
        except:
            return None

    def encrypt_password(self, key=None, new_password=None, save=False):

        if hasattr(self, 'webhook') and self.webhook.use_multi_form:
            key = settings.SECRET_KEY
        else:
            key = key or self.key
            
        if self.password is None:
            return 
        if new_password and self.password != new_password:
            self.password = new_password

        cyphertext = encrypt(self.password, key=key)
        setattr(self, 'password', cyphertext)
        if save:
            self.save()
        return cyphertext

    def decrypt_password(self, key=None):
        if hasattr(self, 'webhook') and self.webhook.use_multi_form:
            key = settings.SECRET_KEY
        else:
            key = key or self.key
        if self.password is None:
            return None
        password = str.encode(self.password)
        return decrypt(password, key=key)

    def get_trigger_kwargs(self, **kwargs):
        return {}
    
    def set_attr(self, **kwargs):
        for attr, value in kwargs.items():
            if not hasattr(self, attr):
                continue 
            try:
                setattr(self, attr, value)
            except:
                pass
    
    def create_task(self, event_trigger=None, webhook=None, **kwargs):
        webhook = getattr(self, 'webhook', webhook)
        event_trigger = event_trigger or getattr(webhook, 'event_trigger', None)

        if webhook and webhook.id is None:
            webhook = None

        self.task = Task(
            webhook = webhook,
            event_trigger = event_trigger,
            status_code = None,
            method_type = self.method_type
        )
        self.task.save()
        return self.task
    
    def get_kwargs(self, **kwargs):
        try:
            trigger_kwargs = self.get_trigger_kwargs(**kwargs)
            self.task.method = self.current_detail
            self.task.save()
            return trigger_kwargs
        except Exception as e:
            self.task.method = self.current_detail
            self.task.status_code = 'err'
            self.task.response = f'System cannot build webhook kwargs :{e}'
            print('System cannot trigger webhook')
            print(e)
            self.task.save()
            self.task.exception_class = str(e.__class__.__name__)
            return False


    def trigger(self, body=None, key=None, **kwargs):
        form = kwargs.get('form')
        self.key = key or self.key
        self.task.created_at = datetime.now()
        self.task.response_time = None
        new_body = copy.deepcopy(body)

        if form:
            include_image_base64 = form.get_settings('ekyc.include_image_base64')

            if include_image_base64:
                try:
                    new_body['extra']['ekyc']['liveness']['image_base64'] = ''
                except Exception:
                    pass

                try:
                    new_body['extra']['ekyc']['identity_document']['image_base64'] = ''
                except Exception:
                    pass

                try:
                    new_body['extra']['ekyc']['identity_document']['face_image_base64'] = ''
                except Exception:
                    pass

                try:
                    new_body['extra']['ekyc']['identity_document_back']['image_base64'] = ''
                except Exception:
                    pass

        self.task.body = new_body

        if self.task.status_code and not status.is_success(self.task.status_code):
            self.task.save()
            return self.task
        
        if form:
            secret_key_str = form.get_settings(
                'encryption.aes_256_cbc_pkcs7_secret_key',
                default=None,
                secure=False,
            )
        else:
            secret_key_str = None

        if secret_key_str:
            aes_cbc = AESCBC(secret_key_str)
            body = aes_cbc.encrypt(json.dumps(body, ensure_ascii=False))
            headers = kwargs.get('headers', {})

            if headers:
                headers.update({'Content-Type': 'text/plain'})
            else:
                kwargs['headers'] = {'Content-Type': 'text/plain'}

        trigger_kwargs = self.get_kwargs(body=body, **kwargs)

        if not trigger_kwargs:
            return self.task

        try:
            res = requests.request(**trigger_kwargs)
            self.task.status_code = res.status_code
            self.task.response = res.text
            self.task.response_time = datetime.now()
        except Exception as e:
            self.task.status_code = 'err'
            self.task.response = str(e)
            self.task.exception_class = str(e.__class__.__name__)

        self.task.save()
        return self.task
