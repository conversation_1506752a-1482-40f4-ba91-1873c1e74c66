<script setup lang="ts">
import { useWindowSize, watchDebounced } from '@vueuse/core';

import { getMaskRectFromScale } from '@ekyc/helpers/face-size-checker';

const LOG_PREFIX = [
  '%c[LIVENESS FaceMaskSvgNext]',
  'background: #000; color: #ffa; border: 1px solid #ffa;',
];

const props = defineProps({
  isFullFrame: {
    type: Boolean,
    default: false,
  },
});

const { height, width } = useWindowSize();

let drawCount = 0;
const maskRef = ref<SVGElement>();

function drawFullFrame() {
  console.log(...LOG_PREFIX, 'Drawing full screen frame');
  // Now, kiosk resolution is 520x810
  const l = Math.min(width.value * 0.046, 24); // =24/520px
  const r = Math.max(width.value * (1 - 0.046), width.value - 24); // =24/520px
  const t = Math.min(height.value * 0.138, 112); // =112/810px
  const b = Math.max(height.value * (1 - 0.138), height.value - 112); // =112/810px

  const cr = Math.min(20, (r - l) / 2, (b - t) / 2); // Ensure cornerRadius is not too large

  if (maskRef.value) {
    maskRef.value.style.left = `${l}px`;
    maskRef.value.style.top = `${t}px`;
    maskRef.value.style.width = `${r - l}px`;
    maskRef.value.style.height = `${b - t}px`;
    maskRef.value.style.borderRadius = `${cr}px`;
  }

  return true;
}

function drawFaceFrame() {
  console.log(...LOG_PREFIX, 'Drawing face mask frame');

  const middleRect = getMaskRectFromScale(document.body.getBoundingClientRect().toJSON());

  const faceLeftX = middleRect.left;

  const faceRightX = middleRect.right;

  const faceTopY = middleRect.top;

  const faceBottomY = middleRect.bottom;

  if (maskRef.value) {
    maskRef.value.style.left = `${faceLeftX}px`;
    maskRef.value.style.top = `${faceTopY}px`;
    maskRef.value.style.width = `${faceRightX - faceLeftX}px`;
    maskRef.value.style.height = `${faceBottomY - faceTopY}px`;
  }
}

function draw() {
  if (props.isFullFrame) {
    drawFullFrame();
  } else {
    drawFaceFrame();
  }

  // Init watch on first draw
  if (drawCount <= 0) {
    watchDebounced([height, width, () => props.isFullFrame], draw, { debounce: 100 });
  }

  drawCount += 1;
}

defineExpose({
  draw,
});
</script>

<template>
  <div class="face-mask-rect">
    <!-- svg will redraw after resize to prevent glitching -->
    <div
      ref="maskRef"
      id="face-mask-rect-face-svg-mask"
      :class="['inner-mask-face', { 'is-full-frame': isFullFrame }]"
    />
  </div>
</template>

<style scoped lang="scss">
.face-mask-rect {
  @apply z-10;
  width: 100%;
  height: 100%;
}

.inner-mask-face {
  @apply absolute;
  @apply border-white border-[3px] border-dashed;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.75);
  border-radius: 50% 50% 50% 50% / 40% 40% 55% 55%;
}

.inner-mask-face.focused {
  @apply border-solid;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 1);
}

.dashed-border-svg {
  @apply z-10;
}

.dashed-border-svg path {
  @apply z-30;
  fill: none;
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 12;
}
</style>
