import { config } from '@vue/test-utils';
import { beforeAll } from 'vitest';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'vitest-canvas-mock';

import i18n from '@core/plugins/i18n';

import '../../src/helpers/polyfills';

config.mocks.$t = k => k;
config.mocks.$tc = k => k;
config.mocks.$te = k => k;

beforeAll(async () => {
  i18n.mergeLocaleMessage('th', await import('@/translations/th.json'));
  i18n.mergeLocaleMessage('en', await import('@/translations/en.json'));
  i18n.mergeLocaleMessage('lo', await import('@/translations/lo.json'));
  i18n.mergeLocaleMessage('zh-hant', await import('@/translations/zh-hant.json'));
  i18n.mergeLocaleMessage('zh-hans', await import('@/translations/zh-hans.json'));
  i18n.mergeLocaleMessage('ms', await import('@/translations/ms.json'));
  i18n.mergeLocaleMessage('ru', await import('@/translations/ru.json'));
  i18n.mergeLocaleMessage('ar', await import('@/translations/ar.json'));
  i18n.mergeLocaleMessage('de', await import('@/translations/de.json'));
  i18n.mergeLocaleMessage('es', await import('@/translations/es.json'));
  i18n.mergeLocaleMessage('fr', await import('@/translations/fr.json'));
  i18n.mergeLocaleMessage('hi', await import('@/translations/hi.json'));
  i18n.mergeLocaleMessage('id', await import('@/translations/id.json'));
  i18n.mergeLocaleMessage('ja', await import('@/translations/ja.json'));
  i18n.mergeLocaleMessage('vi', await import('@/translations/vi.json'));
  i18n.mergeLocaleMessage('km', await import('@/translations/km.json'));
  i18n.mergeLocaleMessage('my', await import('@/translations/my.json'));
});
