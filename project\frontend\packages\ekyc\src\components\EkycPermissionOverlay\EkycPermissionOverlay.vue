<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import ImageSelector from '@helpers/components/ImageSelector.vue';

import { playShowOverlay } from '@ekyc/helpers/animations';

defineProps({
  overrideImages: {
    type: Object as () => Types.EkycComponentSetting['override_images'],
    default: () => ({}),
  },
});

const { t } = useI18n();

onMounted(() => {
  playShowOverlay();
});
</script>

<template>
  <div class="modal permission-overlay is-active">
    <div class="modal-background" />
    <div class="modal-card">
      <div class="toggle-button">
        <div class="font-bold text-base">{{ t('ekyc.recorder.camera_permission_toggle') }}</div>
        <svg
          width="32"
          height="17"
          viewBox="0 0 32 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M7.8664 0.654785C3.54348 0.654785 0.0390625 4.1592 0.0390625 8.48212C0.0390625 12.805 3.54348 16.3095 7.8664 16.3095H23.5211C27.844 16.3095 31.3484 12.805 31.3484 8.48212C31.3484 4.1592 27.844 0.654785 23.5211 0.654785H7.8664ZM23.5211 14.3526C26.7633 14.3526 29.3916 11.7243 29.3916 8.48212C29.3916 5.23993 26.7633 2.61162 23.5211 2.61162C20.2789 2.61162 17.6506 5.23993 17.6506 8.48212C17.6506 11.7243 20.2789 14.3526 23.5211 14.3526Z"
            fill="var(--app-primary-color,#4364E8)"
          />
        </svg>
      </div>

      <img
        v-if="overrideImages?.camera_permission"
        :src="overrideImages.camera_permission"
        class="preview"
        alt="failed"
      />
      <ImageSelector v-else name="selfie" class="w-72 h-72" />

      <div class="font-bold text-base">{{ t('ekyc.recorder.error_permission_denied_title') }}</div>
      <div class="text-sm">{{ t('ekyc.recorder.error_permission_denied_desc') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal {
  @apply p-6;
}

.modal-background {
  background-color: rgba(40, 48, 57, 1) !important;
}

.modal-card {
  @apply w-full p-6 rounded-lg;
  @apply flex flex-col gap-4;
  opacity: 0px;
}

.toggle-button {
  @apply flex justify-between items-center w-5/6;
  @apply py-2 px-4 m-auto rounded-2xl;
  color: var(--color-text);
  background-color: rgba(var(--app-primary-rgb), 0.2);
}
</style>
