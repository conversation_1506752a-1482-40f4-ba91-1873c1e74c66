const getDefaultBaseSettings = (): Types.BaseEkycComponentSetting => ({
  ref: '',
  dummy: false,
  ensure_facecompare: false,
  expose_upload_interface: false,
  upload_url: '',
  auto_url: '',
  log_url: '',
  check_quality_url: null,
  result_tracking_url: '',
  retryMax: 1,
  retryDelay: 5000,
  show_privacy_policy: false,
  watermark_text: '',
  show_popup_desktop: false,
  show_popup_camera_permission: true,
  show_popup_webview: true,
  show_popup_go_to_setting: false,
  override_popup_go_to_setting: {
    content: '',
  },
  show_flip_button: true,
  additional_constraints: {},
  override_messages: {},
  override_images: {} as any,
});

const getDefaultLivenessSettings = (): Types.LivenessComponentSetting => ({
  ...getDefaultBaseSettings(),
  face_actions_url: '',
  result_tracking_url: '',
  log_url: '',
  liveness_log_url: '',
  start_url: '',
  cancel_url: '',
  fail_url: '',
  prepare_top_content: '',
  liveness: {
    backendType: undefined, // auto
    recordTimeMax: {
      idle: 30,
      turn_left: 30,
      turn_right: 30,
      idle_background: 30,
      blink_twice: 30,
      mouth_open: 30,
    },
    waitTimeBetweenActions: 1,
    maxHistorySize: 100,
    enableFaceSize: false,
    enableIdleOnly: false,
    action_sequence: [],
    actionIconSetName: 'smiley',
    checkers: {
      idle: [['eye_open', 'mouth_close', 'angle']],
      turn_left: [['angle']],
      turn_right: [['angle']],
      idle_background: [['angle']],
      blink_twice: [['angle', 'blink_twice']],
      mouth_open: [['mouth_open', 'angle']],
    },
    threshold: {
      idle: {
        yaw_min: 70,
        yaw_max: 110,
        pitch_min: 60,
        pitch_max: 110,
        frames: 10,
        blink_min_score: 0.5,
        mouth_open_min_score: 0.45,
      },
      turn_left: {
        cheek_ratio: 0.07,
        yaw_min: 0,
        yaw_max: 65,
        frames: 1,
      },
      turn_right: {
        cheek_ratio: 0.07,
        yaw_min: 115,
        yaw_max: 180,
        frames: 1,
      },
      idle_background: {
        face_noise: 80,
        yaw_min: 70,
        yaw_max: 110,
        pitch_min: 70,
        pitch_max: 110,
        frames: 2,
        not_turn_left: 0.07,
        not_turn_right: 0.07,
      },
      blink_twice: {
        yaw_min: 50,
        yaw_max: 130,
        pitch_min: 60,
        pitch_max: 110,
        frames: 1,
        blink_min_score: 0.45,
        blink_timeout_ms: 5_000,
      },
      mouth_open: {
        yaw_min: 70,
        yaw_max: 110,
        mouth_open_min_score: 0.5,
        frames: 1,
      },
    },
    face_size: {
      mask_scale_x: 0.45,
      mask_scale_y: 0.55,
      max_mask_x: 0.7,
      small_scale_x: 0.8,
      small_scale_y: 0.1,
      big_scale_x: 1.2,
      big_scale_y: 1.2,
    },
  },
});
const getDefaultDocumentSettings = (): Types.DocumentComponentSetting => ({
  ...getDefaultBaseSettings(),
  frame_size: {
    mask_scale_x: 0.5,
    mask_scale_y: 0.33,
    max_mask_x: 0.9,
  },
});

const getDefaultAllSettings = () => ({
  liveness: getDefaultLivenessSettings(),
  document: getDefaultDocumentSettings(),
  backcard: getDefaultDocumentSettings(),
});

const allSettings = reactive(getDefaultAllSettings());

export const useEkycSettings = () => {
  const defaultSettings = ref(getDefaultAllSettings());

  const waitTimeBetweenActions = computed(
    () => allSettings.liveness.liveness.waitTimeBetweenActions,
  );

  return {
    allSettings,
    defaultSettings,
    waitTimeBetweenActions,
    resetSettings: (media: Types.EkycMediaTypes) => {
      Object.assign(allSettings[media], defaultSettings.value[media]);
    },
  };
};

export default {
  useEkycSettings,
};
