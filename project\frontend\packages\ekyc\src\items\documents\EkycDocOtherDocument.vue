<script setup lang="ts">
import { useVModels } from '@vueuse/core';
import { useI18n } from 'vue-i18n-composable';

import MediaPictureModal from '@ekyc/components/MediaPictureModal/MediaPictureModal.vue';

import { DocumentProps, useCommonEkycItem } from '../../composables/use-common-ekyc-item';
import EkycBase from '../base/EkycBase.vue';

const props = defineProps({
  ...DocumentProps,
});

const { selectedCountry } = useVModels(props);

const emit = defineEmits(['ready']);

const { t } = useI18n();

const ekyc = useCommonEkycItem({
  media: 'document',
  itemType: 'other_document',
  selectedCountry,
});

const { stopChildPreview, isDisplayMediaRecorder, media, itemType, onRecordFinished } = ekyc;

const titleText = computed(() => t('ekyc.other_document.title'));
const descriptionText = computed(() => t('ekyc.other_document.description'));
const suggestionText = computed(() => t('ekyc.other_document.suggestion_text'));
const cancelText = computed(() => t('ekyc.other_document.cancel') as string);

const overlayImage = 'https://cdn.uppass.io/ekyc/assets/common/generic-card-frame.svg';

onMounted(() => {
  emit('ready');
});

onBeforeUnmount(() => {
  stopChildPreview();
});

defineExpose({
  ...ekyc,
  titleText,
  descriptionText,
  suggestionText,
  cancelText,
  overlayImage,
  stopChildPreview,
});
</script>

<template>
  <div>
    <EkycBase media="document">
      <template #modal>
        <MediaPictureModal
          v-if="isDisplayMediaRecorder"
          ref="recorder"
          :media="media"
          :item-type="itemType"
          :cancel-text="cancelText"
          :overlay-image="overlayImage"
          @finish="onRecordFinished"
        />
      </template>
    </EkycBase>
  </div>
</template>

<style lang="scss" scoped>
@import '../base/EkycDocStyle.scss';
</style>
