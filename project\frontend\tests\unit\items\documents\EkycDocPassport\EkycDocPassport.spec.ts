import { mount } from '@vue/test-utils';
import { initLocalVue } from '_tests/unit/utils';
import mapValues from 'lodash/mapValues';
import { beforeEach } from 'node:test';
import { describe, expect, it, vi } from 'vitest';

import EkycDocPassport from '@ekyc/items/documents/EkycDocPassport/EkycDocPassport.vue';

import * as MOCK from './EkycDocPassport.mock';

const MOCK_EKYC_DOC_API_RESULT_BASE = {
  ocr: { key_from_ocr: 'value_from_ocr' },
  data: {
    result: {},
    status: 'ok',
    image_url: {
      img: '',
      face_image: '',
    },
    request_id: '',
  },
  autofill: {
    failed_fields: [],
    success_fields: [],
  },
  result_log: '',
  document_type: 'front_card',
  gateway_result: {
    image_url: {
      img: '',
      face_image: '',
    },
    request_id: '',
  },
} as unknown as Types.EkycDocumentApiResult;

const MOCK_EKYC_DOC_API_RESULT_FAIL = {
  ...MOCK_EKYC_DOC_API_RESULT_BASE,
  data: {
    ...MOCK_EKYC_DOC_API_RESULT_BASE.data,
    result: { ocr: { status: false } },
  },
} as Types.EkycDocumentApiResult;

const mocks = vi.hoisted(() => {
  return {
    ekycService: {
      sendNfcApi: vi.fn(),
    },
  };
});

vi.mock('@ekyc/services/ekyc', async () => {
  return {
    sendNfcApi: mocks.ekycService.sendNfcApi.mockImplementation(
      ({ result, status }: any, token?: string) => {
        if (token === 'MAX_ATTEMPT') {
          return {
            status: false,
            data: {
              error_type: 'max_attempt',
            },
          };
        }
        if (token === 'ERROR') {
          return {
            status: false,
            data: {
              this_request_has_400_error: true,
            },
          };
        }
        return status === 'success'
          ? {
              status: true,
              data: {
                ocr: {
                  ...mapValues(result, val => `new_${val}`),
                  key_from_api: 'new_value_from_api',
                },
              },
            }
          : {
              status: false,
            };
      },
    ),
  };
});

const component = mount(EkycDocPassport as any, initLocalVue(MOCK.PROPS));
type ComponentInstance = InstanceType<typeof EkycDocPassport>;

const vm = component.vm as unknown as ComponentInstance;

const sendInterfaceRequestSpy = vi.spyOn(vm.nfcRef, 'sendInterfaceRequest');
const sendInterfaceRequestPermissionSpy = vi.spyOn(vm.nfcRef, 'sendInterfaceRequestPermission');
const handleResponseNfcSpy = vi.spyOn(vm.nfcRef, 'handleResponseNfc');
const emitErrorSpy = vi.spyOn(vm as any, 'emitError');

const mockPostMessage = vi.fn();
const initMockSdk = () => {
  globalThis.UppassSDK = {
    postMessage: mockPostMessage,
    onmessage: null, // This will be assigned later
  };
};

describe('EkycDocPassport', () => {
  describe('sendInterfaceRequest', async () => {
    const mockPayload = { type: 'request_permission', data: {} } as const;
    const mockStringPayload = JSON.stringify(mockPayload);

    it('No UppassSDK', () => {
      const logSpy = vi.spyOn(console, 'log');
      vm.nfcRef.sendInterfaceRequest(mockPayload);
      expect(globalThis.UppassSDK?.onmessage).toBeFalsy();
      console.log(logSpy.mock.calls);
      expect(logSpy).toHaveBeenCalledWith('No calling ekyc onMessage:', mockStringPayload);
    });

    it('Has UppassSDK', () => {
      initMockSdk();
      vm.nfcRef.sendInterfaceRequest(mockPayload);
      expect(globalThis.UppassSDK?.onmessage).toBeTruthy();
      expect(mockPostMessage).toHaveBeenCalledWith(mockStringPayload);
    });
  });

  it('check selectedCountry watcher', async () => {
    await component.setProps({ selectedCountry: 'IDN' });
    expect(vm.selectedCountry).toBe('IDN');
  });

  it('sendInterfaceRequestPermission', async () => {
    const expectedPayload = JSON.stringify({ type: 'request_permission', data: {} });
    vm.nfcRef.sendInterfaceRequestPermission();
    expect(mockPostMessage).toHaveBeenCalledWith(expectedPayload);
  });

  describe('sendInterfaceRequestNfc', async () => {
    beforeEach(() => {
      vm.pageState = 'none';
      vm.nfcRef.ocrApiResponse = undefined;
    });
    const warnSpy = vi.spyOn(console, 'warn');

    it('Document failed validation', () => {
      vm.nfcRef.sendInterfaceRequestNfc(MOCK_EKYC_DOC_API_RESULT_FAIL);
      expect(vm.pageState).toBe('none');
      expect(vm.nfcRef.ocrApiResponse).toBeUndefined();
      expect(warnSpy).toHaveBeenCalledWith('cancelled, document failed validation!');
    });

    it('Document passed validation', () => {
      vm.nfcRef.sendInterfaceRequestNfc(MOCK_EKYC_DOC_API_RESULT_BASE);
      expect(vm.pageState).toBe('nfc');
      expect(vm.nfcRef.ocrApiResponse).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE);
      expect(sendInterfaceRequestSpy).toHaveBeenCalled();
    });
  });

  describe('waitForPermissionResponse', async () => {
    it('Has permission', async () => {
      vm.pageState = 'permission_popup';

      setTimeout(() => {
        vm.pageState = 'camera';
      }, 500);

      sendInterfaceRequestPermissionSpy.mockClear();
      const res = await vm.waitForPermissionResponse();

      expect(sendInterfaceRequestPermissionSpy).toHaveBeenCalledOnce();
      expect(res).toBeTruthy();
    });

    it('No permission', async () => {
      vm.pageState = 'permission_popup';
      expect(vm.pageState).toBe('permission_popup');

      setTimeout(() => {
        vm.pageState = 'none';
      }, 500);

      sendInterfaceRequestPermissionSpy.mockClear();
      const res = await vm.waitForPermissionResponse();

      expect(sendInterfaceRequestPermissionSpy).toHaveBeenCalledOnce();
      expect(res).toBeFalsy();
    });
  });

  describe('checkNfc', async () => {
    it('Not include NFC', async () => {
      vm.allSettings.document.include_nfc = false;
      vm.pageState = 'none';

      const res = await vm.checkNfc();

      expect(vm.pageState).toBe('camera');
      expect(res).toBeTruthy();
    });

    describe('Include NFC', () => {
      it('Already init interface', async () => {
        const logSpy = vi.spyOn(console, 'log');

        vm.pageState = 'none';
        vm.isInterfaceReady = true;
        vm.allSettings.document.include_nfc = true;

        setTimeout(() => {
          vm.pageState = 'camera';
        }, 500);

        const res = await vm.checkNfc();
        expect(vm.pageState).toBe('camera');

        expect(res).toBeTruthy();
        expect(logSpy).toHaveBeenCalledWith('checkNFC:', 'init interface already');
        expect(vm.isInterfaceReady).toBeTruthy();
      });

      it('Re-init interface', async () => {
        const logSpy = vi.spyOn(console, 'log');

        vm.pageState = 'none';
        vm.isInterfaceReady = false;
        vm.allSettings.document.include_nfc = true;

        setTimeout(() => {
          vm.pageState = 'camera';
        }, 500);

        const res = await vm.checkNfc();
        expect(vm.pageState).toBe('camera');

        expect(res).toBeTruthy();
        expect(logSpy).toHaveBeenCalledWith('checkNFC:', 'init interface');
        expect(vm.isInterfaceReady).toBeTruthy();
        expect(vm.uploadedCallbacks).toContain(vm.nfcRef.sendInterfaceRequestNfc);
      });
    });
  });

  describe('handleResponsePermission', async () => {
    it('All permission granted', async () => {
      const mockPayload = {
        type: 'response_permission',
        data: { camera: true, nfc: true },
      } as const;
      vm.pageState = 'none';

      vm.nfcRef.handleResponsePermission(mockPayload);

      expect(vm.pageState).toBe('camera');
    });

    it('Some permission was not granted', async () => {
      const mockPayload = {
        type: 'response_permission',
        data: { camera: true, nfc: false },
      } as const;
      vm.pageState = 'none';

      vm.nfcRef.handleResponsePermission(mockPayload);

      expect(vm.pageState).toBe('permission_popup');
    });
  });

  describe('callNfcReceiver', async () => {
    it('Response permission case', async () => {
      const mockPayload = new MessageEvent('message', {
        data: JSON.stringify({ type: 'response_permission', data: { camera: true, nfc: true } }),
      });
      vm.pageState = 'none';

      vm.nfcRef.callNfcReceiver(mockPayload);

      expect(vm.pageState).toBe('camera');
    });

    it('OCR step not done (no ocrApiResponse data)', async () => {
      const mockData = { status: 'reading', progress: 10 };
      const mockPayload = new MessageEvent('message', {
        data: JSON.stringify({ type: 'response_nfc', data: mockData }),
      });
      vm.pageState = 'none';
      vm.nfcRef.ocrApiResponse = undefined;

      const res = vm.nfcRef.callNfcReceiver(mockPayload);

      expect(vm.pageState).toBe('none');
      expect(res).toBeUndefined();
    });

    it('Ocr step not done', async () => {
      const mockPayload = new MessageEvent('message', {
        data: JSON.stringify({ type: 'response_permission', data: { camera: true, nfc: true } }),
      });
      vm.nfcRef.ocrApiResponse = undefined;
      vm.pageState = 'none';

      vm.nfcRef.callNfcReceiver(mockPayload);

      expect(handleResponseNfcSpy).not.toHaveBeenCalled();
    });

    it('Response nfc case', async () => {
      const mockData = { status: 'reading', progress: 10 };
      const mockPayload = new MessageEvent('message', {
        data: JSON.stringify({ type: 'response_nfc', data: mockData }),
      });
      vm.nfcRef.nfcLoadingProgress = 0;
      vm.nfcRef.nfcState = 'waiting';
      vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_FAIL };
      vm.pageState = 'none';

      vm.nfcRef.callNfcReceiver(mockPayload);

      expect(vm.nfcRef.nfcLoadingProgress).toBe(10);
      expect(vm.nfcRef.nfcState).toBe('reading');
    });

    it('Error case', async () => {
      const errorSpy = vi.spyOn(console, 'error');
      const mockPayload = new MessageEvent('message', {}) as any;
      vm.pageState = 'none';

      vm.nfcRef.callNfcReceiver(mockPayload);

      expect(errorSpy).toHaveBeenCalledOnce();
    });
  });

  describe('handleResponseNfc', async () => {
    describe('Status reading', () => {
      it('with progress payload', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'reading', progress: 30 },
        } as const;
        vm.nfcRef.nfcState = 'waiting';
        vm.nfcRef.nfcLoadingProgress = 0;

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.nfcRef.nfcState).toBe('reading');
        expect(vm.nfcRef.nfcLoadingProgress).toBe(30);
      });

      it('without progress payload', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'reading', progress: null },
        } as const;
        vm.nfcRef.nfcState = 'waiting';
        vm.nfcRef.nfcLoadingProgress = 10;

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.nfcRef.nfcState).toBe('reading');
        expect(vm.nfcRef.nfcLoadingProgress).toBe(0);
      });
    });

    it('Status waiting', async () => {
      const mockPayload = { type: 'response_nfc', data: { status: 'waiting' } } as const;
      vm.nfcRef.nfcState = 'reading';
      vm.nfcRef.nfcLoadingProgress = 50;

      await vm.nfcRef.handleResponseNfc(mockPayload);

      expect(vm.nfcRef.nfcState).toBe('waiting');
      expect(vm.nfcRef.nfcLoadingProgress).toBe(0);
    });

    it('Status error', async () => {
      const mockToken = 'test_token';
      const mockPayload = {
        type: 'response_nfc',
        data: { status: 'error', code: 'invalid_input_data' },
        token: mockToken,
      } as const;
      vm.nfcRef.nfcLoadingProgress = 50;

      await vm.nfcRef.handleResponseNfc(mockPayload);

      expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, mockToken);
      expect(vm.nfcRef.nfcLoadingProgress).toBe(0);
    });

    describe('Status success', () => {
      it('Has result', async () => {
        const mockToken = 'test_token';
        const mockPayload = {
          type: 'response_nfc',
          data: {
            status: 'success',
            result: { last_name: 'test', key_from_nfc: 'value_from_nfc' },
          },
          token: mockToken,
        } as const;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, mockToken);

        // key_from_ocr should stay the same
        // key_from_nfc should be replaced by api
        // new_key_from_api should be added by api
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual({
          key_from_ocr: 'value_from_ocr',
          lastname: 'new_test',
          key_from_nfc: 'new_value_from_nfc',
          key_from_api: 'new_value_from_api',
        });
      });

      it('Has result - no predefined ocr result', async () => {
        const mockToken = 'test_token';
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'success', result: { key_from_nfc: 'value_from_nfc' } },
          token: mockToken,
        } as const;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE, ocr: undefined };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, mockToken);
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual({
          key_from_nfc: 'new_value_from_nfc',
          key_from_api: 'new_value_from_api',
        });
      });

      it('No result', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'success' } as any,
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual({
          key_from_ocr: 'value_from_ocr',
          key_from_api: 'new_value_from_api',
        });
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, undefined);
      });

      it('NFC Error', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'error', code: 'error_anything' },
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, undefined);
      });

      it('NFC success + API Error', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'success', result: {} },
          token: 'ERROR',
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, 'ERROR');
      });

      it('NFC error + API Error', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'error', code: 'test' },
          token: 'ERROR',
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, 'ERROR');
      });

      it('NFC success + Max attempt', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'success', result: {} },
          token: 'MAX_ATTEMPT',
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, 'MAX_ATTEMPT');
      });

      it('NFC error + Max attempt', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'error', code: 'error_anything' },
          token: 'MAX_ATTEMPT',
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).toHaveBeenCalledWith(mockPayload.data, 'MAX_ATTEMPT');
      });

      it('Server Error (ex. Cant get session token)', async () => {
        const mockPayload = {
          type: 'response_nfc',
          data: { status: 'error', code: 'error_server' },
        } as const;
        vm.isDisplayMediaRecorder = true;
        vm.nfcRef.ocrApiResponse = { ...MOCK_EKYC_DOC_API_RESULT_BASE };
        mocks.ekycService.sendNfcApi.mockClear();

        await vm.nfcRef.handleResponseNfc(mockPayload);

        expect(vm.isDisplayMediaRecorder).toBeFalsy();
        expect(vm.nfcRef.ocrApiResponse?.ocr).toStrictEqual(MOCK_EKYC_DOC_API_RESULT_BASE.ocr);
        expect(mocks.ekycService.sendNfcApi).not.toHaveBeenCalled();
      });
    });
  });

  it('Should call stopChildPreview before unmounted', () => {
    vm.recorder = {
      videoRef: true,
      stopPreview: vi.fn(),
    };
    const stopChilldPreview = vi.spyOn(vm.recorder, 'stopPreview');
    component.destroy();
    expect(stopChilldPreview).toHaveBeenCalledOnce();
  });
});
