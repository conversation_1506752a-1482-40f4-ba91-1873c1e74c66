<script setup lang="ts">
import { Icon } from '@iconify/vue2';

// Props
const props = defineProps({
  icon: {
    type: [String, Array as () => string[]],
    default: '',
  },
  inline: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [Number, String],
    default: null,
  },
  height: {
    type: [Number, String],
    default: null,
  },
});

const resolvedIcon = computed(() => (Array.isArray(props.icon) ? props.icon.join('') : props.icon));
</script>

<template>
  <span class="icon">
    <Icon :icon="resolvedIcon" :inline="inline" :width="width" :height="height" />
  </span>
</template>
