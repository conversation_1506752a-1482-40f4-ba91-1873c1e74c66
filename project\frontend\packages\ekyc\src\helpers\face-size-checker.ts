import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';

import type { FaceLandmarks68 } from './face-api-proxies/classes/FaceLandmarks68';

const { allSettings } = useEkycSettings();

function getFaceWidthThreshold(width: number) {
  if (width >= 1080) {
    return 150;
  }
  // iPhone SE
  if (width >= 607) {
    return 100;
  }
  return 50;
}

export function getMaskRectFromScale(origin: Types.CommonRect) {
  const X_SCALE = allSettings.liveness.liveness.face_size.mask_scale_x;
  const Y_SCALE = allSettings.liveness.liveness.face_size.mask_scale_y;
  const X_MAX = allSettings.liveness.liveness.face_size.max_mask_x;

  const middleX = origin.width / 2;
  const middleY = origin.height / 2;
  const maxOffsetByPercentX = (origin.width / 2) * X_MAX; // xx% of origin
  const maxOffsetByPaddingX = origin.width / 2 - 8; // 8 pixels

  let offsetX = (origin.height / 2) * X_SCALE;
  let offsetY = (origin.height / 2) * Y_SCALE;
  offsetX = Math.min(offsetX, offsetY, maxOffsetByPercentX, maxOffsetByPaddingX); // Should not be wider than height OR max
  offsetY = Math.min(offsetY, offsetX * 1.25); // Should not be much taller than width

  return {
    left: middleX - offsetX,
    right: middleX + offsetX,
    top: Math.max(middleY - offsetY, 0),
    bottom: Math.min(middleY + offsetY, origin.bottom),
  };
}

export function getRectFromScale(
  posOrigin: Types.CommonRect,
  sizeOrigin: Types.CommonRect,
  X_SCALE: number,
  Y_SCALE: number,
) {
  const middleX = posOrigin.width / 2;
  const middleY = posOrigin.height / 2;

  const offsetX = (sizeOrigin.width / 2) * X_SCALE;
  const offsetY = (sizeOrigin.height / 2) * Y_SCALE;

  return {
    left: middleX - offsetX,
    right: middleX + offsetX,
    top: middleY - offsetY,
    bottom: middleY + offsetY,
  } as Types.CommonRect;
}

function getAlignedScaledPoints(
  points: [number, number][],
  srcWidth: number,
  srcHeight: number,
  targetWidth: number,
  targetHeight: number,
) {
  /**
   * * Image size always keep aspect ratio
   *
   * |———————IHHHI———————|
   * |       I   I       |
   * |       I---I ↖     |
   * |———————IHHHI ⇠⇠⇠⇠⇠⇠⇠ landmark will try to keep aspect ratio
   */
  const scale = Math.max(targetWidth / srcWidth, targetHeight / srcHeight);
  const scaledPoints = points.map(([x, y]) => [x * scale, y * scale]);
  const scaledWidth = srcWidth * scale;
  const scaledHeight = srcHeight * scale;

  /**
   * * Image top will align with Active rect top
   * * Active rect will be centered on Image
   *
   * |———————IHHHI ⇠⇠⇠⇠⇠⇠⇠ active rect = screen size
   * |       I   I       |⇠⇠⇠ landmark width = image width
   * |       I---I       |
   * |———————IHHHI———————|
   */

  const offsetX = (scaledWidth - targetWidth) / 2;
  const offsetY = (scaledHeight - targetHeight) / 2;
  const alignedScaledPoints: [number, number][] = scaledPoints.map(([x, y]) => {
    if (x !== 0 || y !== 0) {
      return [x - offsetX, y - offsetY];
    }
    return [x, y];
  });
  return {
    points: alignedScaledPoints,
    rect: {
      left: offsetX,
      right: offsetX + targetWidth,
      top: offsetY,
      bottom: offsetY + scaledHeight,
      width: scaledWidth,
      height: scaledHeight,
      scale,
    } as Types.CommonRect,
  };
}

export function getAlignedScaledPointsFromLandmark(
  landmark: FaceLandmarks68,
  rect: Types.CommonRect,
) {
  return getAlignedScaledPoints(
    landmark.positions.map(p => [p.x, p.y]),
    landmark.imageWidth,
    landmark.imageHeight,
    rect.width,
    rect.height,
  );
}

export function getAlignedScaledPointsFromMesh(
  mesh: Types.Coords3D,
  inputWidth: number,
  inputHeight: number,
  rect: Types.CommonRect,
) {
  return getAlignedScaledPoints(
    mesh.map(p => [p.x * inputWidth, p.y * inputHeight]),
    inputWidth,
    inputHeight,
    rect.width,
    rect.height,
  );
}

function getFaceSizeRect(points: [number, number][]): Types.CommonRect {
  /* Find the reference position of face using extremes */
  const xCoords = points.map(p => p[0]);
  const yCoords = points.map(p => p[1]);

  const leftFaceRect = Math.min(...xCoords);
  const rightFaceRect = Math.max(...xCoords);
  const topFaceRect = Math.min(...yCoords);
  const bottomFaceRect = Math.max(...yCoords);

  const faceWidth = Math.abs(rightFaceRect - leftFaceRect);
  const faceHeightApprox = Math.abs(bottomFaceRect - topFaceRect);

  return {
    left: leftFaceRect,
    right: rightFaceRect,
    top: topFaceRect,
    bottom: bottomFaceRect,
    width: faceWidth,
    height: faceHeightApprox,
  };
}

export function checkFaceTooSmall(
  points: [number, number][],
  smallRect: Types.CommonRect,
  minFaceWidth: number,
) {
  const faceRect = getFaceSizeRect(points);

  /* Conditions */
  const leftFail = faceRect.left > smallRect.left;
  const rightFail = faceRect.right < smallRect.right;
  const topFail = undefined;
  const bottomFail = faceRect.bottom < smallRect.bottom || faceRect.top > smallRect.bottom;
  const faceWidthFail = faceRect.width < minFaceWidth; // Face width is less than THRESHOLD

  const conditions = [leftFail, rightFail, bottomFail, topFail];
  const anySideFail = conditions.some(c => c);
  const isFaceTooSmall = anySideFail || faceWidthFail;

  return {
    fail: isFaceTooSmall,
    fail_condition: {
      left_fail: leftFail,
      right_fail: rightFail,
      top_fail: topFail,
      bottom_fail: bottomFail,
      face_width_fail: faceWidthFail,
    },
    face_rect: faceRect,
  };
}

export function checkFaceTooBig(
  points: [number, number][],
  bigRect: Types.CommonRect,
  maxFaceWidth: number,
) {
  const faceRect = getFaceSizeRect(points);

  /* Conditions */
  const leftFail = faceRect.left < bigRect.left; // Left eye is out of screen
  const rightFail = faceRect.right > bigRect.right; // Right eye is out of screen
  const bottomFail = bigRect.bottom < faceRect.bottom; // Mouth is out of screen
  const topFail = bigRect.top > faceRect.top; // Eye is out of screen
  const faceWidthFail = faceRect.width > maxFaceWidth; // Face width is more than THRESHOLD

  const conditions = [leftFail, rightFail, bottomFail, topFail];
  const anySideFail = conditions.some(c => c);
  const isFaceTooBig = anySideFail || faceWidthFail;

  return {
    fail: isFaceTooBig,
    fail_condition: {
      left_fail: leftFail,
      right_fail: rightFail,
      bottom_fail: bottomFail,
      top_fail: topFail,
      face_width_fail: faceWidthFail,
    },
    face_rect: faceRect,
  };
}

const getIgnoreFailResult = (rect: Types.CommonRect) => ({
  fail: false,
  fail_condition: {
    left_fail: false,
    right_fail: false,
    top_fail: false,
    bottom_fail: false,
    face_width_fail: false,
  },
  face_rect: rect,
});

function checkFaceSizeFromPoints(
  points: [number, number][],
  imageWidth: number,
  rects: Types.FaceMaskRectInfo,
) {
  // Check small face
  const smallScaleX = allSettings.liveness.liveness.face_size.small_scale_x;
  const smallScaleY = allSettings.liveness.liveness.face_size.small_scale_y;
  const smallRect = getRectFromScale(
    rects.active_rect,
    rects.face_mask_rect,
    smallScaleX,
    smallScaleY,
  );
  const minFaceWidth = getFaceWidthThreshold(imageWidth);
  const smallRecord =
    smallScaleX >= 0 || smallScaleY >= 0
      ? checkFaceTooSmall(points, smallRect, minFaceWidth)
      : getIgnoreFailResult(smallRect);

  // Check big face
  const bigScaleX = allSettings.liveness.liveness.face_size.big_scale_x;
  const bigScaleY = allSettings.liveness.liveness.face_size.big_scale_y;
  const bigRect = getRectFromScale(rects.active_rect, rects.face_mask_rect, bigScaleX, bigScaleY);
  const maxFaceWidth = rects.active_rect.width;
  const bigRecord =
    bigScaleX >= 0 || bigScaleY >= 0
      ? checkFaceTooBig(points, bigRect, maxFaceWidth)
      : getIgnoreFailResult(bigRect);

  const isFaceTooSmall = smallRecord.fail;
  const isFaceTooBig = bigRecord.fail;

  const record: Omit<Types.FaceSizeRecord, 'relative_face_rect' | 'relative_face_points'> = {
    fail: isFaceTooSmall || isFaceTooBig,
    too_small_check: {
      ...smallRecord,
      middle_rect: {
        middle_x_scaling: smallScaleX,
        middle_y_scaling: smallScaleY,
        ...smallRect,
      },
    },
    too_big_check: {
      ...bigRecord,
      middle_rect: {
        middle_x_scaling: bigScaleX,
        middle_y_scaling: bigScaleY,
        ...bigRect,
      },
    },
  };
  return { isFaceTooSmall, isFaceTooBig, record };
}

export function checkFaceSizeFromLandmark(
  landmark: FaceLandmarks68,
  rects: Types.FaceMaskRectInfo,
) {
  const activeRect = rects.active_rect;

  const { points, rect } = getAlignedScaledPointsFromLandmark(landmark, activeRect);

  /* Find the reference position of face (ear & mouth) */
  //  36 -> right eye
  //  45 -> left eye
  //  28 -> nose
  //  62 -> mouth
  //  16 -> right ear
  //   0 -> left ear
  const lEar = points[0];
  const rEar = points[16];
  const mouth = points[62];
  // const nose = points[28];

  const { isFaceTooSmall, isFaceTooBig, record } = checkFaceSizeFromPoints(
    [lEar, rEar, mouth],
    landmark.imageWidth,
    rects,
  );

  return {
    isFaceTooSmall,
    isFaceTooBig,
    record: {
      ...record,
      relative_face_points: points
        .filter(([x, y]) => x !== 0 || y !== 0)
        .map(([x, y]) => [+x.toFixed(2), +y.toFixed(2)]),
      relative_face_rect: rect,
    },
  };
}

export function checkFaceSizeFromMesh(
  mesh: Types.Coords3D,
  inputWidth: number,
  inputHeight: number,
  rects: Types.FaceMaskRectInfo,
) {
  const activeRect = rects.active_rect;

  const { points } = getAlignedScaledPointsFromMesh(mesh, inputWidth, inputHeight, activeRect);

  const { isFaceTooSmall, isFaceTooBig, record } = checkFaceSizeFromPoints(
    points,
    inputWidth,
    rects,
  );

  return {
    isFaceTooSmall,
    isFaceTooBig,
    record,
  };
}
