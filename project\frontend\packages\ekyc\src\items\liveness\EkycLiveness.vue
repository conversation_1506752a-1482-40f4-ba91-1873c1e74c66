<template>
  <div>
    <EkycBase media="liveness">
      <template #modal>
        <MediaLivenessModal
          v-if="isDisplayMediaRecorder"
          ref="recorder"
          @finish="onRecordFinished"
        />
      </template>
    </EkycBase>
  </div>
</template>

<script setup lang="ts">
import MediaLivenessModal from '@ekyc/components/MediaLivenessModal.vue';
import { LivenessProps, useCommonEkycItem } from '@ekyc/composables/use-common-ekyc-item';
import { loadModel } from '@ekyc/helpers/models';
import { useFaceApiStore } from '@ekyc/store/modules/faceAPI';

import EkycBase from '../base/EkycBase.vue';

const props = defineProps({
  ...LivenessProps,
});

const emit = defineEmits<{
  (e: 'ready', value?: any): void;
  (
    e: 'error',
    value: {
      media: string;
      code: string;
      error: string;
      detail: string;
    },
  ): void;
}>();

// Override default settings first
const faceApiStore = useFaceApiStore();

const ekyc = useCommonEkycItem({
  media: 'liveness',
  itemType: 'liveness',
});

const { media, isDisplayMediaRecorder, allSettings, onRecordFinished } = ekyc;

async function init() {
  console.log('[Liveness] Mounting...');
  if (allSettings.liveness.dummy) {
    console.log('Ekyc.Liveness has dummy setting enabled, wont load Face API model!');
    emit('ready');
    return;
  }
  try {
    const loadModelResult = await loadModel();
    emit('ready', loadModelResult);
  } catch (err) {
    faceApiStore.setCanLoadModel(false);
    console.error(err);
    emit('error', {
      media: 'liveness',
      code: 'error_liveness_load_models',
      error: 'ekyc.recorder.error_liveness_load_models',
      detail: err?.toString(),
    });
  }
}

onMounted(() => {
  init();
});

defineExpose({
  ...ekyc,
});
</script>
