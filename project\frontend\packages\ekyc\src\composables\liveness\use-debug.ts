// Info
const debugInfo = reactive({
  faceResults: [] as Types.DetectFunctionResult[],
  actionLogs: [] as Types.MethodLog[][],
  status: '...',
  state: '...',
  video: null as HTMLVideoElement,
  initLog: null as any,
  faceMaskRect: null as Types.FaceMaskRectInfo,
  faceSizeRecord: null as Types.FaceSizeRecord,
});

// Config
const allowDebugMode = ref<boolean>(false);
const debugMode = ref<boolean>(false);
const debugOverlay = ref<any>();
const freezeTime = ref<boolean>(false);
const forcePass = ref<boolean>(false);
const dontPass = ref<boolean>(false);

const clickDebugCounter = ref(0);
const clickDebugTimeout = ref<number>(null);

export const useDebug = () => {
  function setDebugInfo<K extends keyof typeof debugInfo>(key: K, value: (typeof debugInfo)[K]) {
    debugInfo[key] = value;
  }

  function setCanvasSize(w: number, h: number) {
    if (debugOverlay.value) {
      debugOverlay.value.debugCanvas.width = w;
      debugOverlay.value.debugCanvas.height = h;
    }
  }

  function drawRectangle(rect: { x: number; y: number; width: number; height: number }) {
    if (debugOverlay.value) debugOverlay.value.drawRectangle(rect);
  }

  function clickDebug() {
    if (allowDebugMode.value) {
      clickDebugCounter.value += 1;
      clearTimeout(clickDebugTimeout.value);
      clickDebugTimeout.value = setTimeout(() => {
        clickDebugCounter.value = 0;
      }, 500) as any;
      if (clickDebugCounter.value >= 10) {
        clickDebugCounter.value = 0;
        debugMode.value = !debugMode.value;
        console.log('[DEBUG MODE]:', debugMode.value);
      }
    }
  }

  return {
    debugInfo,

    allowDebugMode,
    debugMode,
    debugOverlay,
    freezeTime,
    forcePass,
    dontPass,
    setDebugInfo,
    setCanvasSize,
    drawRectangle,
    clickDebug,
    clickDebugCounter,
  };
};

export default useDebug;
