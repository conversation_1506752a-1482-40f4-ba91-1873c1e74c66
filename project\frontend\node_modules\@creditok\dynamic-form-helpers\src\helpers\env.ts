/**
 * ensures the app isn't built with invalid env vars.
 */
import { z } from 'zod';

const envSchema = z.object({
  // VITE_APP_SENTRY_ENVIRONMENT: z.string().default('localhost'),
});

const env = envSchema.safeParse(import.meta.env);

if (!env.success) {
  throw new Error(
    '❌ Invalid environment variables: ' + JSON.stringify(env.error.format(), null, 4),
  );
}
export const FRONTEND_ENV = env.data;
