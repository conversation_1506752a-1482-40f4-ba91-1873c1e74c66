<script setup lang="ts">
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import { getEndpoint } from '@core/helpers/get-endpoint';

import { VueMonacoEditor, type monacoEditor } from '@/plugins/monaco-editor';

const { http, addAlert } = useDynamicFormApp();

const code = ref('');
const isLoading = ref(false);

const singleRef = shallowRef<monacoEditor.editor.IStandaloneCodeEditor>();
const configs = reactive({ wrap: false });

const monacoEditorOptions = computed<monacoEditor.editor.IEditorOptions>(() => ({
  automaticLayout: true,
  formatOnType: true,
  formatOnPaste: true,
  stickyScroll: {
    enabled: true,
    maxLineCount: 5,
  },
  readOnly: isLoading.value,
}));

function onMonacoMounted(
  editor: monacoEditor.editor.IStandaloneCodeEditor,
  monaco: typeof monacoEditor,
) {
  singleRef.value = editor;
  // eslint-disable-next-line no-bitwise
  editor.addAction({
    label: 'Toggle Wrap',
    id: 'toggleWrap',
    contextMenuGroupId: '1_uppass',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyW],
    run: () => {
      configs.wrap = !configs.wrap;
      editor.updateOptions({ wordWrap: configs.wrap ? 'on' : 'off' });
    },
  });
  editor.addAction({
    label: 'Format & Save',
    id: 'formatAndSave',
    contextMenuGroupId: '1_uppass',
    keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],
    run: () => {
      configs.wrap = !configs.wrap;
      editor.updateOptions({ wordWrap: configs.wrap ? 'on' : 'off' });
    },
  });
  editor.onKeyDown(e => {
    if (e.ctrlKey && e.keyCode === monaco.KeyCode.KeyS) {
      saveStyle();
    }
  });
}

function onMonacoEditorChanged() {}

async function getStyle() {
  isLoading.value = true;
  try {
    const res = await http.get(getEndpoint('STYLE'));
    code.value = res.data?.css;
  } catch (err) {
    console.error(err);
    addAlert('Failed to load style');
  } finally {
    isLoading.value = false;
  }
}

async function saveStyle() {
  try {
    if (!confirm('Are you sure you want to save these style changes?')) {
      return;
    }

    isLoading.value = true;

    await http.post(getEndpoint('STYLE'), {
      css: code.value,
    });
    addAlert({ message: 'Style saved successfully!', type: 'success' });
  } catch (err) {
    console.error(err);
    const { data } = err.response;
    addAlert(data);
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  getStyle();
});
</script>

<template>
  <div class="style-editor">
    <div class="container">
      <section class="data-table">
        <div class="data-table__header">
          <h1 class="title is-4">Update Application Style</h1>
          <hr />
        </div>
        <div class="field is-grouped is-grouped-right">
          <b-button
            class="button is-primary"
            icon-left="mdi:content-save"
            :loading="isLoading"
            :disabled="isLoading"
            @click.prevent="saveStyle"
          >
            Save
          </b-button>
        </div>
        <div class="field">
          <VueMonacoEditor
            :value.sync="code"
            :options="monacoEditorOptions"
            language="css"
            theme="vs-dark"
            width="100%"
            height="70vh"
            @mount="onMonacoMounted"
            @change="onMonacoEditorChanged"
          />
        </div>
      </section>
    </div>
  </div>
</template>
