import { useLivenessState } from './use-liveness-state';

export const useFramesSequences = () => {
  const { snapFrameNumber } = useLivenessState();

  const actionFramesSequences = ref<Types.ActionSequence[]>([]);
  const startActionName = ref('');

  const missingFramesSequences = ref<Types.MissingFramesSequence[]>([]);
  const startMissingFrame = ref(-1);

  function processActionFrame(action: string) {
    const frame = snapFrameNumber.value;
    const isChanged = startActionName.value !== action;
    if (isChanged) {
      startActionName.value = action;
      actionFramesSequences.value.push({
        action,
        min_frame: frame,
        max_frame: frame,
      });
    } else {
      actionFramesSequences.value[actionFramesSequences.value.length - 1].max_frame = frame;
    }
  }

  function processMissingFrameByFaceResults(results: any[]) {
    const frame = snapFrameNumber.value;
    const wasMissing = startMissingFrame.value >= 0;
    const isMissingNow = results.length <= 0;

    // Not Missing
    if (!wasMissing && !isMissingNow) {
      return;
    }

    // Started Missing
    if (!wasMissing && isMissingNow) {
      startMissingFrame.value = frame;
      missingFramesSequences.value.push([frame, frame]);
      return;
    }

    // Continue Missing
    if (wasMissing && isMissingNow) {
      missingFramesSequences.value[missingFramesSequences.value.length - 1][1] = frame;
      return;
    }

    // Ended Missing
    if (wasMissing && !isMissingNow) {
      startMissingFrame.value = -1;
    }
  }

  return {
    actionFramesSequences,
    missingFramesSequences,
    processActionFrame,
    processMissingFrameByFaceResults,
  };
};

export default { useFramesSequences };
