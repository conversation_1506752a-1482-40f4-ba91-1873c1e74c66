import { sentryVitePlugin } from '@sentry/vite-plugin';
import legacy from '@vitejs/plugin-legacy';
import vue from '@vitejs/plugin-vue2';
import path from 'path';
import AutoImport from 'unplugin-auto-import/vite';
import DefineOptions from 'unplugin-vue-define-options/vite';
import type { UserConfig } from 'vite';
import { checker } from 'vite-plugin-checker';
import CircleDependency from 'vite-plugin-circular-dependency';

const isProduction = process.env.NODE_ENV === 'production';
const enableConsole = !isProduction || [1, true, '1', 'true'].includes(process.env.ENABLE_CONSOLE);
console.log(`[Frontend] Enable console: ${enableConsole}`);

const publicDir = process.env.PUBLIC_DIR || '../public/dist/';

// https://vitejs.dev/config/
const baseConfig = {
  base: process.env.PUBLIC_PATH || '/dist/',
  plugins: [
    DefineOptions(),
    AutoImport({
      imports: [
        // presets
        'vue',
      ],
      eslintrc: {
        enabled: false, // enable if need updates
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      },
      dts: false, // 'packages/helpers/src/types/auto-imports.d.ts', // enable if need updates
    }),
    vue(),
    CircleDependency(),
    // chunkSplitPlugin(),
    // manualChunksPlugin(),
    // vite-plugin-checker
    // https://github.com/fi3ework/vite-plugin-checker
    checker({
      // typescript: true,
      // vueTsc: true,
      // eslint: { lintCommand: 'eslint' },
      // stylelint: {lintCommand: 'stylelint'},
    }),
    legacy({
      targets: ['defaults', 'not IE 11', 'iOS 15'],
      // iOS 15 considered modern, but it lacks array.at, we need to polyfill it
      modernPolyfills: ['es.array.at'],
    }),
    sentryVitePlugin({
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: 'uppass',
      project: 'uppass-frontend',

      telemetry: false,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@core': path.resolve(__dirname, './packages/core/src'),
      '@builder': path.resolve(__dirname, './packages/builder/src'),
      '@ekyc': path.resolve(__dirname, './packages/ekyc/src'),
      '@helpers': path.resolve(__dirname, './packages/helpers/src'),
      'vue-i18n-composable': path.resolve(
        __dirname,
        './packages/helpers/src/helpers/vue-i18n-composable.ts',
      ),
      _tests: path.resolve(__dirname, './tests'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
    dedupe: ['vue-demi'],
  },
  define: {
    // This is necessary in Vue 2 codebases. It is automatic in Vue 3
    __VUE_PROD_DEVTOOLS__: 'false',
  },
  build: {
    manifest: 'manifest.json',
    outDir: publicDir,
    minify: 'terser',
    sourcemap: true,
    emptyOutDir: true,
    terserOptions: {
      // https://github.com/webpack-contrib/terser-webpack-plugin#terseroptions
      compress: {
        typeofs: false,
        drop_debugger: !enableConsole,
        drop_console: !enableConsole,
      },
      ie8: true,
      safari10: true,
    },
    rollupOptions: {
      input: {
        client: path.resolve(__dirname, 'main-client.ts'),
        dashboard: path.resolve(__dirname, 'main-dashboard.ts'),
      },
      output: {
        // libraryExport: 'default',
        experimentalMinChunkSize: 1_000_000,
        dir: publicDir,
        chunkFileNames: isProduction ? 'upc.[hash:7].js' : 'c-[name].[hash:7].js',
        entryFileNames: isProduction ? 'upe.[hash].js' : 'e-[name].[hash].js',
        assetFileNames: isProduction ? 'assets/upa.[hash].[ext]' : 'assets/a-[name].[hash].[ext]',
      },
      // manualChunks(id, { getModuleInfo }) {
      //   // Define chunk groups with their patterns
      //   const CHUNK_GROUPS = {
      //     // Core packages
      //     'pkg-core': {
      //       patterns: ['packages/core'],
      //     },
      //     'pkg-builder': {
      //       patterns: ['packages/builder'],
      //     },
      //     'pkg-ekyc': {
      //       patterns: ['packages/ekyc'],
      //     },
      //     'pkg-helpers': {
      //       patterns: ['packages/helpers'],
      //     },
      //     // Styles
      //     styles: {
      //       patterns: ['assets/styles/', '.scss$', '.css$'],
      //     },
      //   };

      //   // Check if module belongs to a predefined chunk group
      //   for (const [groupName, group] of Object.entries(CHUNK_GROUPS)) {
      //     if (group.patterns.some(p => new RegExp(p).test(id))) {
      //       return groupName;
      //     }
      //   }

      //   return null;
      // },
      // },
      // external: [],
    },
    // ** Default logic except svg is always ignored for dynamic color ** //
    assetsInlineLimit(filePath, content) {
      if (filePath.endsWith('.html')) return false;
      if (filePath.endsWith('.svg')) return false;
      return content.length < 4096;
    },
    // cssCodeSplit: false,
  },

  optimizeDeps: {
    include: [],
    exclude: [],
  },

  server: {
    allowedHosts: undefined,
    headers: {
      // Enable for Sentry profiling
      'Document-Policy': 'js-profiling',
    },
    cors: {
      origin: ['http://localhost:8000', 'http://localhost:8443'],
    },
  },
} satisfies UserConfig;

if (process.env.LOCAL_ALLOWED_HOST) {
  console.log(`[Frontend] Has LOCAL_ALLOWED_HOST=${process.env.LOCAL_ALLOWED_HOST}`);
  baseConfig.server.allowedHosts = [process.env.LOCAL_ALLOWED_HOST];
}

if (process.env.LOCAL_CORS_ORIGIN) {
  console.log(`[Frontend] Has LOCAL_CORS_ORIGIN=${process.env.LOCAL_CORS_ORIGIN}`);
  baseConfig.server.cors.origin.push(process.env.LOCAL_CORS_ORIGIN);
}

export default baseConfig;
