<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import ImageSelector from '@helpers/components/ImageSelector.vue';

import { playShowOverlay } from '@ekyc/helpers/animations';

defineProps({
  overrideImages: {
    type: Object as () => Types.EkycComponentSetting['override_images'],
    default: () => ({}),
  },
});

const { t } = useI18n();
const emit = defineEmits(['yes']);

onMounted(() => {
  playShowOverlay();
});
</script>

<template>
  <div class="modal camera-retry-overlay is-active">
    <div class="modal-background" />
    <div class="modal-card">
      <slot name="image">
        <img
          v-if="overrideImages?.camera_retry"
          :src="overrideImages.camera_retry"
          class="preview"
          alt="failed"
        />
        <ImageSelector v-else name="failed-timeout" />
      </slot>

      <div class="font-bold text-base">
        <slot name="title">{{ t('ekyc.recorder.camera_load_failed') }}</slot>
      </div>

      <div class="text-sm">
        <slot name="desc">{{ t('ekyc.recorder.camera_load_failed_desc') }} </slot>
      </div>

      <div class="modal-footer-action">
        <button id="retry-btn" class="button" @click.prevent="emit('yes')">
          {{ t('ekyc.recorder.camera_load_failed_action') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal {
  @apply p-6;
}

.modal-background {
  background-color: rgba(40, 48, 57, 1) !important;
}

.modal-card {
  @apply w-full p-6 rounded-lg;
  @apply flex flex-col gap-4;
  opacity: 0px;
}

.modal-footer-action {
  @apply w-full;
}

#retry-btn {
  @apply w-full !max-w-[unset];
  @apply text-[var(--app-button-text-color)];
  @apply bg-[var(--app-button-background-color)];
  @apply border-none;
}
</style>
