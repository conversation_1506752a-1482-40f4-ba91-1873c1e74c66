{"version": 3, "sources": ["../../lodash/_baseGet.js"], "sourcesContent": ["var castPath = require('./_castPath'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\nmodule.exports = baseGet;\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,QAAQ;AAUZ,aAAS,QAAQ,QAAQ,MAAM;AAC7B,aAAO,SAAS,MAAM,MAAM;AAE5B,UAAI,QAAQ,GACR,SAAS,KAAK;AAElB,aAAO,UAAU,QAAQ,QAAQ,QAAQ;AACvC,iBAAS,OAAO,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,MACtC;AACA,aAAQ,SAAS,SAAS,SAAU,SAAS;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}