/* eslint-disable camelcase */

function getPerformanceLog() {
  try {
    const log = {};
    Object.assign(log, {
      memory: JSON.parse(
        JSON.stringify((window.performance as any)?.memory, [
          'totalJSHeapSize',
          'usedJSHeapSize',
          'jsHeapSizeLimit',
        ]),
      ),
    });
    return log;
  } catch (error) {
    console.warn('Can not get performance log.', error);
    return null;
  }
}

export class LivenessLog {
  action: string | string[];

  color: string;

  frame_number: number;

  recorded_at: Date;

  result = {
    checker_logs: {} as any[][] | undefined,

    action_progress: 0,
    action_progress_ms: 0,

    image_width: 0,
    image_height: 0,

    liveness_time_spent: 0,
    frame_delta_ms: 0,
    log_delta_ms: 0,
    time_left: 0,

    passed: false,
    finished: false,
    state: '',
    next_state: '',

    face_mask_rect: {
      active_rect: {},
      overlay_rect: {},
      face_mask_rect: {},
    },
    face_size_record: null as Types.FaceSizeRecord,

    landmarks: [] as [number, number][][],
    detections: [] as Types.FaceDetectionResult[],

    log_version: '2023-08-08',

    performance: getPerformanceLog(),

    // Last log info
    error_message: undefined as string | null,
    action_sequences: undefined as Types.ActionSequence[],
    missing_frames_sequences: undefined as Types.MissingFramesSequence[],
  };

  constructor(action: string | string[], frame_number: number, color = '00000000') {
    this.action = action;
    this.color = color;
    this.frame_number = frame_number;
    this.recorded_at = new Date();
  }

  assignInfo(data: Partial<(typeof this)['result']>) {
    Object.assign(this.result, data);
  }

  setInfoByRecordedData(recordedData: Types.RecordedData) {
    this.result.image_width = recordedData.canvas.width;
    this.result.image_height = recordedData.canvas.height;
  }

  setInfoByFaceDetectionRecord(detectionRecord: Types.FaceDetectionRecord) {
    const latestDetection = detectionRecord.detections?.slice(-1) || [];
    this.result = {
      ...this.result,
      detections: latestDetection.map(d => ({
        ...d,
        recordedData: undefined,
        landmarks: undefined,
      })),
      landmarks: latestDetection.map(
        d => d.landmarks?.positions?.filter(p => p.x !== 0 || p.y !== 0).map(p => [p.x, p.y]) || [],
      ),
      passed: detectionRecord.passed,
      finished: detectionRecord.finished,
      face_mask_rect: detectionRecord.face_mask_rect,
      face_size_record: detectionRecord.face_size_record,

      frame_delta_ms: detectionRecord.frame_delta_ms,
      state: detectionRecord.state,
      next_state: detectionRecord.next_state,
      checker_logs: detectionRecord.checker_logs,
      action_progress: detectionRecord.action_progress,
      action_progress_ms: detectionRecord.action_progress_ms,
      missing_frames_sequences: detectionRecord.missing_frames_sequences,
    };
  }
}

export default { LivenessLog };
