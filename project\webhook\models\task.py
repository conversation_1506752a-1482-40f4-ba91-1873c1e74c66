from django.db import models
import json
import string
import random
from pydash import objects
from project.custom_logger import logger

NOUNCE_LENGTH = 30


class Task(models.Model):
    webhook = models.ForeignKey("Webhook", on_delete=models.PROTECT, null=True, blank=True)
    body = models.JSONField(null=True, blank=True)
    status_code = models.CharField(max_length=4, null=True, db_index=True)
    event_trigger = models.CharField(max_length=191)
    method = models.JSONField(null=True, blank=True, default=dict)
    response = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, db_index=True)
    method_type = models.CharField(max_length=50, default="custom", blank=True)
    nounce = models.CharField(max_length=30, db_index=True, null=True, default=None, blank=True)

    is_mockup = False
    # request_time = None == created_at
    response_time = None
    exception_class = None

    @property
    def method_name(self):
        if self.webhook:
            return self.webhook.name
        return ""

    @property
    def url(self):
        return self.method.get("url", None)

    def save(self, *args, **kwargs):
        if not self.nounce:
            self.nounce = self.gen_nounce()
        super().save(*args, **kwargs)

    def gen_nounce(self):
        def random_nounce_str():
            letters_and_digits = string.ascii_letters + string.digits
            return "".join(random.choice(letters_and_digits) for i in range(NOUNCE_LENGTH))  # NOSONAR

        def random_nounce():
            nounce = random_nounce_str()
            if Task.objects.filter(nounce=nounce).exists():
                return random_nounce()
            return nounce

        return random_nounce()

    def resend(self, key=None):
        from . import METHODS

        Method = METHODS.get(self.method_type, None)
        if not Method:
            return
        method = Method(
            webhook=self.webhook,
        )
        method.set_attr(**self.method)
        task = method.create_task(event_trigger=self.event_trigger)

        # Update nounce and created_at in body
        objects.set_(self.body, "event.nounce", task.nounce)
        objects.set_(self.body, "event.created_at", task.created_at.isoformat())

        # Recreate extra data if present
        form = None
        if objects.get(self.body, "extra"):
            try:
                from dynamicform.submodules.appliedform.models.appliedform import AppliedForm

                ref = objects.get(self.body, "application.slug")
                applied_form = AppliedForm.objects.filter(slug=ref).first()
                form = applied_form.form
                if applied_form:
                    extra = applied_form.get_extra()
                    objects.set_(self.body, "extra", extra)
            except Exception as e:
                logger.error("Task resend recreate extra error", e)

        method.trigger(body=self.body, key=key, form=form)
        return task

    def get_response(self):
        try:
            return json.loads(self.response)
        except:
            return self.response
