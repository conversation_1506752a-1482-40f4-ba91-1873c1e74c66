import {
  type MaybeRef,
  promiseTimeout,
  useElementSize,
  useRafFn,
  useWindowSize,
  watchDebounced,
} from '@vueuse/core';
import isEmpty from 'lodash/isEmpty';
import merge from 'lodash/merge';
import type { Ref } from 'vue';
import 'webrtc-adapter';

import { getScreenInfo } from '@helpers/helpers/screen';
import { getUserAgent } from '@helpers/helpers/user-agent';
import { Sentry } from '@helpers/plugins/sentry';

import { getDeviceSpecialFlagsForCamera } from '@ekyc/helpers/get-device-special-flags';

import { usePopup } from './use-popup';

export type Options = {
  videoRef: Ref<HTMLVideoElement>;
  highQuality: boolean;
  startFacingMode: VideoFacingModeEnum;
  overrideConstraints: MaybeRef<MediaStreamConstraints>;
  workingConstraints: MaybeRef<MediaStreamConstraints>;
};

export type MediaDeviceInfoValue = Pick<MediaDeviceInfo, 'deviceId' | 'groupId' | 'kind' | 'label'>;

interface ExtendedMediaTrackCapabilities extends MediaTrackCapabilities {
  focusMode?: string[];
  focusDistance?: {
    max: number;
    min: number;
    step: number;
  };
}

interface ExtendedMediaTrackSettings extends MediaTrackSettings {
  focusMode?: string;
}

type ConstraintBuilderMethod = {
  method: string;
  con?: MediaStreamConstraints | null;
  getter?: () => MediaStreamConstraints | null;
};

export type MediaDeviceInfoDetailed = {
  deviceConstraints: MediaStreamConstraints;
  device: MediaDeviceInfoValue;
  tracks: {
    label: string;
    id: string;
    contentHint: string;
    kind: string;
    enabled: boolean;
    muted: boolean;
    readyState: MediaStreamTrackState;
    constraints: MediaTrackConstraints;
    capabilities: ExtendedMediaTrackCapabilities;
    settings: MediaTrackSettings;
  }[];
  success: boolean;
  blocking: {
    fake: boolean;
    reason: string;
  };
  error?: string;
};

const DELAY_BEFORE_REQUEST_MS = 100;
const CAMERA_REQUEST_TIMEOUT_MS = 10_000;

const mediaStream = ref<MediaStream>();

const cameraBlockRules = reactive<Types.CameraBlockRule>({
  default: true,
  label: false,
});

export function setCameraBlockRules(rules: Partial<typeof cameraBlockRules>) {
  Object.assign(cameraBlockRules, rules);
}

let cameraRequestId = ref(0);

export const useCamera = ({
  videoRef,
  highQuality,
  startFacingMode,
  overrideConstraints,
  workingConstraints,
}: Options) => {
  const { width: windowWidth, height: windowHeight } = useWindowSize();
  const { width: videoWidth, height: videoHeight } = useElementSize(videoRef);
  const { showPopupCameraLockdown } = usePopup();

  // Check supports
  let supports: MediaTrackSupportedConstraints;
  try {
    supports = navigator.mediaDevices.getSupportedConstraints();
  } catch (error) {
    console.error('[EKYC_CAMERA] Error getting supported constraints:', error);
    Sentry.addBreadcrumb({
      message: '[EKYC_CAMERA] Error getting supported constraints:',
      type: 'error',
      level: 'error',
      category: 'camera',
      data: error,
    });
    showPopupCameraLockdown.value = true;
  }

  const createCameraLog = () => {
    cameraRequestId.value += 1;
    return {
      request_id: cameraRequestId.value,
      log_version: '2025-08-04',
      screen: getScreenInfo(),
      supports,
      facingMode: facingMode.value,
      lastWorkingConstraints: unref(workingConstraints),
      computeStyle: null as 'video' | 'window' | null,
      transform: null as string | null,
      devices: [] as MediaDeviceInfoDetailed[],
      attempts: [],
      stopPreview: {
        startAt: undefined,
        clearedSrc: undefined,
        clearedIn: undefined,
        clearedEx: undefined,
        errorSrc: undefined,
        errorIn: undefined,
        errorEx: undefined,
        error: undefined,
      },
      enumerateDevices: {
        startAt: undefined,
        retryCount: 0,
        success: undefined,
        error: undefined,
      },
      deviceFlags: undefined as any,
      error: undefined,
    };
  };

  let _latestCameraLog: ReturnType<typeof createCameraLog>;
  const latestCameraLog = () => {
    if (!_latestCameraLog) {
      _latestCameraLog = createCameraLog();
    }
    return _latestCameraLog;
  };

  // Data: Camera-related
  const isVideoPlayed = ref<boolean>(false);
  const streamLoaded = computed<boolean>(() => !!mediaStream.value);

  const deviceCapabilities = ref<ExtendedMediaTrackCapabilities>();
  const deviceSettings = ref<MediaTrackSettings>();
  const facingMode = ref(startFacingMode);
  const isFlipped = computed(() => facingMode.value === 'user');
  const isFlippedManually = ref(false);
  const rotateAmount = ref(0);
  const zoomAmount = ref(1);
  const isCameraReady = ref<boolean>(false);

  const isSwitchCameraSupported = ref<boolean>(supports?.facingMode === true);

  async function stopPreview({ tryRequestAndStopExternal = true } = {}) {
    latestCameraLog().stopPreview.startAt = new Date();
    try {
      if (videoRef.value) {
        console.log('[EKYC_CAMERA] Clearing video src');
        videoRef.value.srcObject = null;
        videoRef.value.src = '';
        isCameraReady.value = false;
        latestCameraLog().stopPreview.clearedSrc = true;
      }
    } catch (err) {
      console.warn('[EKYC_CAMERA] Clearing video src error...', err);
      Sentry.addBreadcrumb({
        message: '[EKYC_CAMERA] Clearing video src error...',
        type: 'error',
        level: 'error',
        category: 'camera',
        data: err,
      });
      const errMsg = err?.message || 'Failed to clear video src';
      latestCameraLog().stopPreview.clearedEx = false;
      latestCameraLog().stopPreview.errorSrc = errMsg;
    }

    if (streamLoaded.value) {
      try {
        console.log('[EKYC_CAMERA] Stopping internal stream');
        mediaStream.value.getTracks().forEach(track => track.stop());
        mediaStream.value = null;
        latestCameraLog().stopPreview.clearedIn = true;
      } catch (err) {
        console.warn('[EKYC_CAMERA] Stopping internal stream error...', err);
        Sentry.addBreadcrumb({
          message: '[EKYC_CAMERA] Stopping internal stream error...',
          type: 'error',
          level: 'error',
          category: 'camera',
          data: err,
        });
        const errMsg = err?.message || 'Failed to stop internal stream';
        latestCameraLog().stopPreview.clearedEx = false;
        latestCameraLog().stopPreview.error = errMsg;
      }
    } else if (tryRequestAndStopExternal) {
      try {
        console.log('[EKYC_CAMERA] Stopping external stream');
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        stream.getTracks().forEach(trackItem => trackItem.stop());
        latestCameraLog().stopPreview.clearedEx = true;
      } catch (err) {
        console.warn('[EKYC_CAMERA] Stopping external stream error...', err);
        Sentry.addBreadcrumb({
          message: '[EKYC_CAMERA] Stopping external stream error...',
          type: 'error',
          level: 'error',
          category: 'camera',
          data: err,
        });
        const errMsg = err?.message || 'Failed to stop external stream';
        latestCameraLog().stopPreview.clearedEx = false;
        latestCameraLog().stopPreview.error = errMsg;
      }
    }
  }

  function checkCameraBlocking(device: MediaDeviceInfoValue) {
    if (cameraBlockRules.label || cameraBlockRules.default) {
      return {
        fake: ['mockup', 'virtual'].some(x => device.label.toLowerCase().includes(x)),
        reason: 'label',
      };
    }

    return { fake: false, reason: 'skip' };
  }

  async function getVideoDevices() {
    console.log('[EKYC_CAMERA] getVideoDevices');

    try {
      console.log('[EKYC_CAMERA] enumerateDevices....');
      latestCameraLog().enumerateDevices.startAt = new Date();
      const allDevices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = allDevices.filter(device => device.kind === 'videoinput');
      const filteredDevices = videoDevices.map(device => {
        return { device, blocking: checkCameraBlocking(device) };
      });
      console.log('[EKYC_CAMERA] enumerateDevices result', filteredDevices);
      latestCameraLog().enumerateDevices.success = true;
      return { devices: filteredDevices };
    } catch (err) {
      console.error('[EKYC_CAMERA] Error enumerating media devices', err);
      Sentry.addBreadcrumb({
        message: '[EKYC_CAMERA] Error enumerating media devices',
        type: 'error',
        level: 'error',
        category: 'camera',
        data: err,
      });
      const errMsg = err?.message || 'Failed to enumerate media devices';
      latestCameraLog().enumerateDevices.success = false;
      latestCameraLog().enumerateDevices.error = errMsg;
      return { devices: [] };
    }
  }

  async function getVideoDevicesWithRetry() {
    const RETRY_DELAY = 200;
    const RETRY_MAX = 3;

    let retryCount = 0;

    while (retryCount < RETRY_MAX) {
      latestCameraLog().enumerateDevices.retryCount = retryCount;
      const { devices } = await getVideoDevices();
      if (devices.length > 0) {
        return { devices };
      } else {
        await promiseTimeout(RETRY_DELAY);
        retryCount += 1;
      }
    }

    return { devices: [] };
  }

  async function setOptimalFocus(videoTrack: MediaStreamTrack) {
    // Only attempt to optimize focus for environment-facing cameras
    if (facingMode.value !== 'environment') {
      return { focused: false, constraints: null };
    }

    try {
      // Skip if already in continuous mode
      const currentSettings = videoTrack.getSettings() as ExtendedMediaTrackSettings;
      if (currentSettings.focusMode === 'continuous') {
        console.log('[EKYC_CAMERA] Already in continuous focus mode');
        return { focused: false };
      }

      // Get the capabilities after we've selected the camera
      const capabilities = videoTrack.getCapabilities() as ExtendedMediaTrackCapabilities;

      // Check if we have access to focus modes and focus distance
      if (capabilities?.focusMode && capabilities?.focusDistance) {
        const focusModes = capabilities.focusMode;
        const hasContinuous = focusModes.includes('continuous');

        const hasManual = focusModes.includes('manual');
        const maxFocus = capabilities.focusDistance.max || 0;

        let focusConstraints = {};

        // Prefer continuous focus mode if available
        if (hasContinuous) {
          focusConstraints = { focusMode: 'continuous' };
          await videoTrack.applyConstraints({ advanced: [focusConstraints] });
          console.log('[EKYC_CAMERA] Applied continuous focus mode');
          return { focused: true, constraints: focusConstraints };
        }
        // Fall back to manual focus if continuous isn't available
        else if (hasManual && maxFocus > 0.1) {
          // Set optimal focus for ID card/document scanning (slightly near focus)
          const OPTIMAL_FOCUS = 0.1; // Closer focus for documents
          focusConstraints = { focusMode: 'manual', focusDistance: OPTIMAL_FOCUS };
          await videoTrack.applyConstraints({ advanced: [focusConstraints] });
          console.log('[EKYC_CAMERA] Applied manual focus with distance:', OPTIMAL_FOCUS);
          return { focused: true, constraints: focusConstraints };
        }
      }

      return { focused: false, constraints: null };
    } catch (error) {
      console.warn('[EKYC_CAMERA] Error setting optimal focus:', error);
      return { focused: false, constraints: null, error: error.toString() };
    }
  }

  async function applyConstraints(con: any) {
    console.log('Apply Constraints:', con);
    const tracks = mediaStream.value?.getTracks();
    const videoTrack = tracks[0];
    await videoTrack.applyConstraints(con);

    deviceSettings.value = videoTrack.getSettings();
    deviceCapabilities.value = videoTrack.getCapabilities();
  }

  async function tryToReFocus(videoTrack: MediaStreamTrack) {
    // Use the optimized focus function
    const { focused, constraints, error } = await setOptimalFocus(videoTrack);

    // Update device settings and capabilities after focus adjustments
    if (focused) {
      deviceSettings.value = videoTrack.getSettings();
      deviceCapabilities.value = videoTrack.getCapabilities();
      return {
        reFocused: true,
        reIso: false,
        reConstraints: constraints,
        error,
      };
    }

    return {
      reFocused: false,
      reIso: false,
      reConstraints: false,
      error,
    };
  }

  function getConstraintForCameraByForceLabel(
    devices: MediaDeviceInfoValue[],
    matchPatterns: string[],
  ) {
    for (const pattern of matchPatterns) {
      const device = devices.find(d => d.label.toLowerCase().includes(pattern));
      if (device) {
        return {
          video: {
            deviceId: { exact: device.deviceId },
          },
        } satisfies MediaStreamConstraints;
      }
    }
    return null;
  }

  function getConstraintForCameraByBestLabel(devices: MediaDeviceInfoValue[]) {
    try {
      // Skip processing if no devices available
      if (!devices || devices.length === 0) {
        console.log('[EKYC_CAMERA] No camera devices found');
        return null;
      }

      // ===== CAMERA MATCHING PATTERNS =====
      // High-priority exact patterns with very high success rates
      const EXACT_PATTERNS = {
        environment: [
          'camera2 0, facing back',
          'camera 0, facing back',
          'back camera 0',
          'camera2 api2, facing back',
          'back dual wide camera',
          'back ultra wide camera',
          'back triple camera',
        ],
        user: [
          'camera2 1, facing front',
          'camera 1, facing front',
          'front camera 0',
          'camera2 api2, facing front',
          'front camera',
          'selfie camera',
        ],
      };

      // Camera quality indicators (often present in main/best cameras)
      const QUALITY_INDICATORS = ['ultra wide', 'pro', 'wide', 'main', 'primary', 'triple', 'dual'];

      // Multilingual keywords for camera detection
      const DIRECTION_KEYWORDS = {
        environment: [
          // English
          'rear',
          'back',
          // European languages
          'rück',
          'arrière',
          'trasera',
          'trás',
          'traseira',
          'posteriore',
          'achterzijde',
          'baksidan',
          'bagside',
          'tylny',
          'takakamera',
          'zadní',
          'darrere',
          'zadná',
          'stražnja',
          'hátsó',
          'spate',
          'πίσω',
          // Asian languages
          '后面',
          '後面',
          '背面',
          '后置',
          '後置',
          '背置',
          '후',
          'belakang',
          'หลัง',
          // Middle Eastern
          'задней',
          'خلف',
          'arka',
          'sau',
          'bak',
          'אחורית',
          'задня',
          'बैक',
        ],
        user: [
          // English
          'front',
          'selfie',
          'face',
          // European languages
          'vorder',
          'avant',
          'frontal',
          'frente',
          'frontale',
          'voorzijde',
          'framsidan',
          'forside',
          'przedni',
          'etukamera',
          'přední',
          'davant',
          'predná',
          'prednja',
          'elülső',
          'față',
          'μπροστά',
          // Asian languages
          '前面',
          '前置',
          '自拍',
          '셀카',
          '전면',
          'depan',
          'หน้า',
          // Middle Eastern
          'передней',
          'أمام',
          'ön',
          'trước',
          'קדמית',
          'передня',
          'फ्रंट',
        ],
      };

      // Standard patterns that commonly indicate cameras
      const COMMON_PATTERNS = {
        environment: ['facing back', 'camera 0', 'camera2 0', '0, back', 'camera 2', 'camera rear'],
        user: ['facing front', 'camera 1', 'camera2 1', '1, front', 'camera front'],
      };

      // Determine which set of patterns to use based on facingMode
      const targetMode = facingMode.value as 'user' | 'environment';

      // ===== MATCHING ALGORITHM =====
      const scoredDevices = devices.map(device => {
        const label = device.label.toLowerCase();
        let score = 0;
        let matchReason = '';

        // 1. Exact high-priority matches (highest confidence)
        // If a device has label: "Camera2 0, facing back"
        // This would match the EXACT_PATTERNS entry "camera2 0, facing back"
        // Score: +1000
        // matchReason: "exact pattern: camera2 0, facing back"
        for (const pattern of EXACT_PATTERNS[targetMode]) {
          if (label.includes(pattern)) {
            score += 1000;
            matchReason = `exact pattern: ${pattern}`;
            break; // Found highest priority match
          }
        }

        // 2. Quality indicator + direction keyword combinations
        // If a device has label: "Ultra Wide Back Camera"
        // This combines the QUALITY_INDICATORS entry "ultra wide" with the BACK_KEYWORDS entry "back"
        // Score: +500
        // matchReason: "quality: ultra wide, keyword: back"

        // Another example - device label: "Triple Camera Trasera"
        // Combines QUALITY_INDICATORS entry "triple" with BACK_KEYWORDS entry "trasera" (Spanish for "back")
        // Score: +500
        // matchReason: "quality: triple, keyword: trasera"
        for (const quality of QUALITY_INDICATORS) {
          if (label.includes(quality)) {
            for (const keyword of DIRECTION_KEYWORDS[targetMode]) {
              if (label.includes(keyword)) {
                score += 500;
                matchReason = matchReason || `quality: ${quality}, keyword: ${keyword}`;
                break;
              }
            }
          }
        }

        // 3. Common pattern matches
        // If a device has label: "Camera 0"
        // Matches COMMON_PATTERNS entry "camera 0"
        // Score: +200
        // matchReason: "common pattern: camera 0"

        // Another example - device label: "Generic Camera, Facing Back"
        // Matches COMMON_PATTERNS entry "facing back"
        // Score: +200
        // matchReason: "common pattern: facing back"
        for (const pattern of COMMON_PATTERNS[targetMode]) {
          if (label.includes(pattern)) {
            score += 200;
            matchReason = matchReason || `common pattern: ${pattern}`;
          }
        }

        // 4. Any direction keyword matches as lowest priority
        // If a device has label: "Samsung Rear Lens"
        // Matches BACK_KEYWORDS entry "rear"
        // Score: +100
        // matchReason: "back keyword: rear"

        // Another example - device label: "Xiaomi 背面 Camera"
        // Matches BACK_KEYWORDS entry "背面" (Chinese for "back")
        // Score: +100
        // matchReason: "back keyword: 背面"
        for (const keyword of DIRECTION_KEYWORDS[targetMode]) {
          if (label.includes(keyword)) {
            score += 100;
            matchReason = matchReason || `direction keyword: ${keyword}`;
            break;
          }
        }

        // Bonus for higher resolution cameras in the name (if present)
        // If a device has label: "48MP Back Camera"
        // The regular expression /\d+\s*mp/ extracts "48MP"
        // The parseInt(mpMatch[1], 10) gets the value 48
        // Score bonus: +240 (48 * 5)
        // matchReason addition: ", 48MP detected"

        // Another example - device label: "Back Triple Camera 12 MP"
        // Extracts "12 MP"
        // Score bonus: +60 (12 * 5)
        // matchReason addition: ", 12MP detected"
        const mpRegex = /(\d+)\s*mp/;
        const mpMatch = mpRegex.exec(label);
        if (mpMatch?.[1]) {
          const mpValue = parseInt(mpMatch[1], 10);
          score += mpValue * 5; // Higher MP = higher score
          matchReason += `, ${mpValue}MP detected`;
        }

        return { device, score, label, matchReason };
      });

      // Sort by score descending (highest first)
      scoredDevices.sort((a, b) => b.score - a.score);

      // Log results for debugging
      console.log('[EKYC_CAMERA] Available devices:', devices.length);
      console.log(
        '[EKYC_CAMERA] Scored devices:',
        scoredDevices.map(d => `${d.label} (score: ${d.score}, reason: ${d.matchReason})`),
      );

      // Choose the highest scoring device with a non-zero score
      const bestDevice = scoredDevices.find(d => d.score > 0);
      if (bestDevice) {
        console.log(`[EKYC_CAMERA] Selected ${targetMode} camera: ${bestDevice.label}`);
        return {
          video: {
            deviceId: { exact: bestDevice.device.deviceId },
          },
        } satisfies MediaStreamConstraints;
      }

      return null;
    } catch (error) {
      console.error('[EKYC_CAMERA] Error finding best camera:', error);
      return null;
    }
  }

  async function tryRequestCameraWithConstraints({
    constraints,
    deviceMethod,
    resolutionMethod,
    reloadStream,
  }: {
    constraints: MediaStreamConstraints;
    deviceMethod: string;
    resolutionMethod: string;
    reloadStream: boolean;
  }) {
    // Preparation
    const attemptLog = {
      deviceMethod,
      resolutionMethod,
      reloadStream,
      reFocusError: undefined,
      fallbackError: undefined,
      playError: undefined,
      streamTracks: undefined,
      startAt: new Date(),
      requestedAt: undefined,
      refocusedAt: undefined,
      playedAt: undefined,
      endAt: undefined,
      error: undefined,
      success: undefined,
      constraints: undefined,
      video: undefined,
    };
    latestCameraLog().attempts.push(attemptLog);

    // Request stream
    try {
      if (reloadStream) {
        const builtCon = merge(
          {},
          {
            audio: false,
            video: {
              facingMode: facingMode.value,
              width: { min: 320 },
              height: { min: 240 },
            },
          },
          constraints,
        );
        console.log('[EKYC_CAMERA] builtConstraints:', JSON.stringify(builtCon, null, 2));
        attemptLog.constraints = builtCon;
        const newStream = await navigator.mediaDevices.getUserMedia(builtCon);
        mediaStream.value = newStream;
      } else {
        console.log('[EKYC_CAMERA] using last stream');
      }

      attemptLog.requestedAt = new Date();

      const stream = mediaStream.value;
      const tracks = stream.getTracks();
      const videoTrack = tracks[0];
      attemptLog.streamTracks = tracks.map(track => ({
        label: track.label,
        id: track.id,
      }));

      deviceSettings.value = videoTrack.getSettings();
      deviceCapabilities.value = videoTrack.getCapabilities();

      const { reFocused, reIso, reConstraints, error } = await tryToReFocus(videoTrack);
      attemptLog.refocusedAt = new Date();
      attemptLog.reFocusError = error;

      if (!videoRef.value) return false;
      try {
        videoRef.value.srcObject = stream;
      } catch (error) {
        // Fallback for old browser
        console.warn('[EKYC_CAMERA] Old browser, using srcObject fallback', error);
        videoRef.value.src = URL.createObjectURL(stream as any);
        attemptLog.fallbackError = error.toString();
      }

      // Fallback for iPhone low-power cant use autoplay
      if (!videoRef.value) return false;
      setTimeout(async () => {
        try {
          await videoRef.value.play();
          videoRef.value.onplay = () => {
            videoRef.value.disablePictureInPicture = true;
          };
        } catch (error) {
          attemptLog.playError = error.toString();
        }
      }, 0);

      attemptLog.playedAt = new Date();

      await new Promise(resolve => {
        const checkVideoReadyInterval = setInterval(() => {
          if (!videoRef.value) {
            clearInterval(checkVideoReadyInterval);
            resolve(false);
          }
          if (videoRef.value?.readyState === 4) {
            console.log(
              `[EKYC_CAMERA] Camera loaded: ${videoRef.value.videoWidth}x${videoRef.value.videoHeight}`,
            );
            isCameraReady.value = true;
            clearInterval(checkVideoReadyInterval);
            resolve(true);
          }
        }, 100);
      });

      if (!videoRef.value) return false;
      attemptLog.endAt = new Date();
      attemptLog.success = true;
      attemptLog.video = {
        width: videoRef.value.width,
        height: videoRef.value.height,
        videoWidth: videoRef.value.videoWidth,
        videoHeight: videoRef.value.videoHeight,
        clientWidth: videoRef.value.clientWidth,
        clientHeight: videoRef.value.clientHeight,
        offsetWidth: videoRef.value.offsetWidth,
        offsetHeight: videoRef.value.offsetHeight,
        autoplay: videoRef.value.autoplay,
        playbackQuality: videoRef.value.getVideoPlaybackQuality(),
        playsInline: videoRef.value.playsInline,
        poster: videoRef.value.poster,
        buffered: videoRef.value.buffered,
        controls: videoRef.value.controls,
        crossOrigin: videoRef.value.crossOrigin,
        currentSrc: videoRef.value.currentSrc,
        currentTime: videoRef.value.currentTime,
        defaultMuted: videoRef.value.defaultMuted,
        defaultPlaybackRate: videoRef.value.defaultPlaybackRate,
        disableRemotePlayback: videoRef.value.disableRemotePlayback,
        duration: videoRef.value.duration,
        ended: videoRef.value.ended,
        error: videoRef.value.error,
        loop: videoRef.value.loop,
        mediaKeys: videoRef.value.mediaKeys,
        muted: videoRef.value.muted,
        paused: videoRef.value.paused,
        playbackRate: videoRef.value.playbackRate,
        played: videoRef.value.played,
        preload: videoRef.value.preload,
        preservesPitch: videoRef.value.preservesPitch,
        readyState: videoRef.value.readyState,
        networkState: videoRef.value.networkState,
        remote: videoRef.value.remote,
        seekable: videoRef.value.seekable,
        seeking: videoRef.value.seeking,
        volume: videoRef.value.volume,
        reFocused,
        reIso,
        reConstraints,
        settings: deviceSettings.value,
      };
      console.log(`[EKYC_CAMERA] Camera log`, attemptLog);
      return true;
    } catch (error) {
      console.error('[EKYC_CAMERA] Camera sub-request failed: ', error);
      attemptLog.endAt = new Date();
      attemptLog.error = error.toString();
      attemptLog.success = false;
      return false;
    }
  }

  async function doRequestSequences({ reloadStream = true } = {}) {
    console.log('[EKYC_CAMERA] doRequestSequences');
    let shouldReloadStream = reloadStream || !mediaStream.value;
    isCameraReady.value = false;

    // Stop preview before requesting camera
    if (shouldReloadStream) {
      await stopPreview({ tryRequestAndStopExternal: true });
    }

    // Delay before page ready
    await promiseTimeout(DELAY_BEFORE_REQUEST_MS);

    // Filter Blocking before getting full detail
    const { devices } = await getVideoDevicesWithRetry();
    const allVideoDevices = devices.map(
      ({ device: { deviceId, groupId, kind, label }, blocking }) => ({
        device: { deviceId, groupId, kind, label },
        blocking,
      }),
    );
    latestCameraLog().devices = allVideoDevices as any;

    const realVideoDevices = allVideoDevices
      .filter(({ blocking }) => !blocking.fake)
      .map(({ device }) => device);

    // if devices already empty, try to getUserMedia anyway
    // if actually filtered all fake devices, reject now
    if (realVideoDevices.length === 0 && allVideoDevices.length !== 0) {
      const errMsg = `No real device found among ${allVideoDevices.length} devices`;
      console.warn('[EKYC_CAMERA]', errMsg);
      latestCameraLog().error = errMsg;
      return false;
    }

    // Device Methods - Modified to avoid populateDetails()
    const deviceMethods: ConstraintBuilderMethod[] = [
      {
        method: 'default',
        con: {},
      },
    ];

    if (shouldReloadStream) {
      deviceMethods.unshift({
        method: 'match_label',
        getter: () => getConstraintForCameraByBestLabel(realVideoDevices),
      });
    }

    // Logic only for back camera
    if (facingMode.value === 'environment') {
      // Add force_label on special devices
      const deviceData = getUserAgent();
      const flags = getDeviceSpecialFlagsForCamera(deviceData);
      if (flags.forceCameraLabel) {
        latestCameraLog().deviceFlags = flags;
        deviceMethods.unshift({
          method: 'force_label',
          getter: () =>
            getConstraintForCameraByForceLabel(realVideoDevices, [flags.forceCameraLabel]),
        });
      }
    }

    // Resolution Methods
    const resolutionMethods: ConstraintBuilderMethod[] = [
      {
        method: 'override',
        getter: () =>
          !isEmpty(unref(overrideConstraints))
            ? (unref(overrideConstraints) as MediaStreamConstraints)
            : null,
      },
      {
        method: 'best',
        con: {
          video: highQuality
            ? { width: { ideal: 4096 }, height: { ideal: 2160 } }
            : { width: { ideal: 1920 }, height: { ideal: 1080 } },
        },
      },
      { method: 'worst', con: { video: true } },
      { method: 'scale', con: { video: { width: { ideal: 1920 }, height: { ideal: 1080 } } } },
      { method: 'scale', con: { video: { width: { ideal: 1440 }, height: { ideal: 810 } } } },
      { method: 'scale', con: { video: { width: { ideal: 1600 }, height: { ideal: 900 } } } },
      { method: 'scale', con: { video: { width: { ideal: 1366 }, height: { ideal: 768 } } } },
      { method: 'scale', con: { video: { width: { ideal: 1280 }, height: { ideal: 720 } } } },
    ];

    // Try to find the optimal camera based on the device's capabilities
    for (const d of deviceMethods) {
      // Try resolutions from highest to lowest
      for (const r of resolutionMethods) {
        console.log('[EKYC_CAMERA] sequence requesting:', d.method, r.method);
        const deviceCon = d.con ?? d.getter?.();
        if (!deviceCon) {
          continue;
        }
        const resCon = r.con ?? r.getter?.();
        if (!resCon) {
          continue;
        }
        const con = merge({}, deviceCon, resCon);
        const success = await tryRequestCameraWithConstraints({
          constraints: con,
          deviceMethod: d.method,
          resolutionMethod: r.method,
          reloadStream: shouldReloadStream,
        });
        if (success) {
          if (isRef(workingConstraints)) {
            workingConstraints.value = con;
          }
          console.log('[EKYC_CAMERA] workingConstraints:', JSON.stringify(con, null, 2));
          return true;
        } else {
          console.log('[EKYC_CAMERA] first attempt failed, force reload stream');
          shouldReloadStream = true;
        }
      }
    }

    console.log('[EKYC_CAMERA] No working constraints found');
    latestCameraLog().error = 'No working constraints found';
    return false;
  }

  async function loadCamera({ reloadStream = true } = {}) {
    isCameraReady.value = false;

    // Re-define facing mode (override from schema)
    const overrideConstraintsObj = unref(overrideConstraints) as MediaStreamConstraints;
    const overrideConstraintsVideoObj = overrideConstraintsObj?.video as MediaTrackConstraints;
    const overridedFacingMode: VideoFacingModeEnum = [
      'environment',
      'left',
      'right',
      'user',
    ].includes(overrideConstraintsVideoObj?.facingMode as VideoFacingModeEnum)
      ? (overrideConstraintsVideoObj?.facingMode as VideoFacingModeEnum)
      : facingMode.value;

    facingMode.value = overridedFacingMode;

    console.log('[EKYC_CAMERA] Loading...');

    try {
      const success = await Promise.race([
        // Try to load camera within 10s.
        doRequestSequences({ reloadStream }),
        // Timeout
        promiseTimeout(CAMERA_REQUEST_TIMEOUT_MS).then(() => {
          latestCameraLog().error = 'error_open_camera_timeout';
        }),
      ]);
      if (success) {
        return {
          success: true,
          logs: latestCameraLog(),
        };
      } else {
        throw new Error(latestCameraLog().error ?? 'No working constraints found');
      }
    } catch (error) {
      console.error('[EKYC_CAMERA] Error loading camera:', error);
      Sentry.addBreadcrumb({
        message: '[EKYC_CAMERA] Error loading camera',
        type: 'error',
        level: 'error',
        category: 'camera',
        data: latestCameraLog(),
      });
    }

    return {
      success: false,
      logs: latestCameraLog(),
    };
  }

  function switchCameraSide() {
    facingMode.value = facingMode.value === 'user' ? 'environment' : 'user';
    return loadCamera();
  }

  function rotateCamera(rotation: number) {
    rotateAmount.value = rotation;
  }

  function zoomCamera(zoom: number) {
    zoomAmount.value = zoom;
  }

  function flipCamera(flip: boolean) {
    isFlippedManually.value = flip;
  }

  let hasModernComputedStyle = true;
  let computeStyleFn: () => string;
  function transformCamera() {
    if (!videoRef.value) return;
    let transformStr: string;
    try {
      // Modern browsers
      computeStyleFn = () =>
        videoRef.value.computedStyleMap().get('transform').toString() ?? 'none';
      transformStr = computeStyleFn();
    } catch {
      // Fallback for older browsers
      try {
        hasModernComputedStyle = false;
        computeStyleFn = () => window.getComputedStyle(videoRef.value).transform ?? 'none';
        transformStr = computeStyleFn();
      } catch {
        // Shouldn't be here
        transformStr = 'none';
      }
    }
    const transforms =
      typeof transformStr === 'string' && transformStr !== 'none'
        ? transformStr.replaceAll(', ', ',').split(' ')
        : [];

    // Flip
    let flipCss = isFlipped.value ? 'rotate3d(0,1,0,180deg)' : '';
    if (isFlippedManually.value) {
      flipCss = isFlipped.value ? '' : 'rotate3d(0,1,0,180deg)';
    }
    const index = transforms.findIndex(t => t.startsWith('rotate3d(0,1,0,'));
    if (index > -1) {
      transforms[index] = flipCss;
    } else {
      transforms.push(flipCss);
    }

    // Rotate
    if (rotateAmount.value !== 0) {
      const rotateCss = `rotate3d(0,0,1,${rotateAmount.value}deg)`;
      const index = transforms.findIndex(t => t.startsWith('rotate3d(0,0,1,'));
      if (index > -1) {
        transforms[index] = rotateCss;
      } else {
        transforms.push(rotateCss);
      }
    }

    // Zoom
    if (zoomAmount.value > 0 && zoomAmount.value !== 1) {
      const zoomCss = `scale(${zoomAmount.value})`;
      const index = transforms.findIndex(t => t.startsWith('scale('));
      if (index > -1) {
        transforms[index] = zoomCss;
      } else {
        transforms.push(zoomCss);
      }
    }

    videoRef.value.style.transform = transforms.join(' ');

    const finalTransform = computeStyleFn();
    latestCameraLog().transform = finalTransform;
    latestCameraLog().computeStyle = hasModernComputedStyle ? 'video' : 'window';
    console.log('[EKYC_CAMERA] transformCamera:', finalTransform);
  }

  function initOnResizeWatch(
    containerWidth: Ref<number> = windowWidth,
    containerHeight: Ref<number> = windowHeight,
  ) {
    function computeResize() {
      if (!videoRef.value) return;

      let vWidth = videoWidth.value;
      let vHeight = videoHeight.value;
      let cWidth = containerWidth.value;
      let cHeight = containerHeight.value;
      const rotated90 = rotateAmount.value % 180 === 90;

      if (rotated90) {
        [vWidth, vHeight] = [vHeight, vWidth];
      }

      let widthGap = cWidth - vWidth;
      let heightGap = cHeight - vHeight;
      if (widthGap < 0 && heightGap === 0) return;
      if (heightGap < 0 && widthGap === 0) return;

      let shouldFullWidth = widthGap > heightGap;
      let widthCss = shouldFullWidth ? `${cWidth}px` : 'unset';
      let heightCss = shouldFullWidth ? 'unset' : `${cHeight}px`;

      if (rotated90) {
        [widthCss, heightCss] = [heightCss, widthCss];
      }

      videoRef.value.style.width = widthCss;
      videoRef.value.style.height = heightCss;
    }

    watch([videoWidth, videoHeight], computeResize);
    watchDebounced([containerWidth, containerHeight], computeResize, { debounce: 200 });
    // Note: Debouncing caused visual flickering on start

    watch(
      [isFlipped, rotateAmount, zoomAmount],
      () => {
        transformCamera();
      },
      { immediate: true },
    );
  }

  const { pause: pauseVideoStateCheckInterval } = useRafFn(() => {
    if (videoRef.value?.readyState >= 1) {
      isVideoPlayed.value = true;
      pauseVideoStateCheckInterval();
    }
  }, {});

  return {
    videoRef,

    mediaStream,
    isVideoPlayed,
    streamLoaded,
    supports,
    deviceCapabilities,
    deviceSettings,
    isSwitchCameraSupported,
    facingMode,
    isFlipped,
    isCameraReady,

    stopPreview,
    loadCamera,
    switchCameraSide,
    rotateCamera,
    zoomCamera,
    flipCamera,
    applyConstraints,
    initOnResizeWatch,
  };
};

export default { useCamera };
