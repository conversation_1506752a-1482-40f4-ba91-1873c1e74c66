// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CreatorStepDocumentNext (builder) > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "type": "ekyc_document_next",
  },
  "fields": {
    "date_of_birth": "date_of_birth",
    "date_of_expiry": "date_of_expiry",
    "date_of_issue": "date_of_issue",
    "document_numbers": "document_numbers",
    "ekyc_document_item": "ekyc_document",
    "full_name": "full_name",
    "full_name_en_first_name": "full_name_en_first_name",
    "full_name_en_last_name": "full_name_en_last_name",
    "gender": "gender",
    "home_address": "home_address",
    "laser_number": false,
    "name_prefix": "name_prefix",
  },
  "name": "ekyc_document_next",
  "sections": {
    "ekyc_document_next": {
      "description": "Description",
      "hide_description": true,
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "ekyc_document": {
          "accepted_countries": {
            "border_pass": [],
            "ci_passport": [],
            "driver_license": [],
            "front_card": [],
            "immigration_card": [],
            "monk_card": [],
            "other_document": [],
            "passport": [
              "GRC",
              "GRL",
              "GUM",
              "KHM",
              "GTM",
              "QAT",
              "GHA",
              "GAB",
              "CPV",
              "GUY",
              "GIN",
              "GNB",
              "CUW",
              "COG",
              "CRI",
              "COM",
              "KAZ",
              "KIR",
              "CUB",
              "KGZ",
              "KWT",
              "GEO",
              "JOR",
              "JAM",
              "DJI",
              "CHN",
              "TCD",
              "SXM",
              "CHL",
              "SMR",
              "WSM",
              "SAU",
              "ZWE",
              "SDN",
              "SSD",
              "SUR",
              "JPN",
              "TTO",
              "TON",
              "TLS",
              "TUR",
              "TUN",
              "TUV",
              "TJK",
              "VAT",
              "NOR",
              "NAM",
              "NRU",
              "NIC",
              "NZL",
              "BRA",
              "BWA",
              "BIH",
              "BGD",
              "BGR",
              "BRB",
              "BHR",
              "BHS",
              "BDI",
              "BFA",
              "PAK",
              "PAN",
              "PNG",
              "PRY",
              "PLW",
              "FRA",
              "MMR",
              "FJI",
              "FIN",
              "PHL",
              "BTN",
              "MNG",
              "MSR",
              "MNE",
              "MRT",
              "MLT",
              "MDV",
              "MDG",
              "MLI",
              "MAC",
              "MYS",
              "GIB",
              "UGA",
              "UKR",
              "RWA",
              "PSE",
              "BOL",
              "LUX",
              "LVA",
              "LIE",
              "LTU",
              "LBY",
              "VUT",
              "LKA",
              "SWZ",
              "CHE",
              "SWE",
              "RUS",
              "NPL",
              "ARE",
              "USA",
              "DOM",
              "GBR",
              "TZA",
              "COD",
              "LAO",
              "MDA",
              "MKD",
              "MWI",
              "SYR",
              "IRN",
              "CZE",
              "CAF",
              "ZAF",
              "VEN",
              "POL",
              "SGP",
              "ESP",
              "SVK",
              "SVN",
              "VGB",
              "MHL",
              "CYM",
              "TCA",
              "VIR",
              "SLB",
              "AUT",
              "AUS",
              "AND",
              "AFG",
              "ABW",
              "ARG",
              "AZE",
              "ARM",
              "ITA",
              "IND",
              "IDN",
              "IRQ",
              "ISR",
              "GNQ",
              "EGY",
              "UZB",
              "URY",
              "ASM",
              "HND",
              "HUN",
              "HKG",
              "GRD",
              "PRK",
              "KOR",
              "IMN",
              "KEN",
              "LCA",
              "VCT",
              "KNA",
              "SHN",
              "SRB",
              "STP",
              "SLE",
              "SYC",
              "SEN",
              "DNK",
              "TKM",
              "BRN",
              "NLD",
              "BEN",
              "BLR",
              "BLZ",
              "BEL",
              "BMU",
              "PER",
              "PRI",
              "MUS",
              "MEX",
              "DEU",
              "YEM",
              "LBN",
              "LSO",
              "ESH",
              "VNM",
              "ECU",
              "ETH",
              "ERI",
              "SLV",
              "EST",
              "HTI",
              "GMB",
              "CAN",
              "CMR",
              "ZMB",
              "MAF",
              "AIA",
              "AGO",
              "ATG",
              "DZA",
              "ALB",
              "CIV",
              "HRV",
              "COL",
              "SOM",
              "DMA",
              "TGO",
              "BES",
              "PRT",
              "MOZ",
              "MCO",
              "MAR",
              "ROU",
              "OMN",
              "CYP",
              "TWN",
              "THA",
              "NGA",
              "NER",
              "FSM",
              "LBR",
              "ISL",
              "IRL",
            ],
            "residence_permit": [],
            "thai_alien_card": [],
            "travel_document": [],
            "white_card": [],
            "work_permit_book": [],
            "work_permit_card": [],
          },
          "autofill_map": [
            {
              "dest": "nid",
              "src": "nid",
            },
            {
              "dest": "document_number",
              "src": "document_number",
            },
            {
              "dest": "name_prefix",
              "src": "title",
            },
            {
              "dest": "full_name_first_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "firstname",
            },
            {
              "dest": "full_name_last_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "lastname",
            },
            {
              "dest": "full_name_middle_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "middlename",
            },
            {
              "dest": "full_name_full",
              "params": {
                "flag_equal": "full",
                "flag_name": "name_type",
              },
              "src": "fullname",
            },
            {
              "dest": "full_name_type",
              "src": "name_type",
            },
            {
              "dest": "full_name_show_middle_name",
              "src": "show_middle_name",
            },
            {
              "dest": "date_of_birth",
              "src": "date_of_birth",
            },
            {
              "dest": "date_of_issue",
              "src": "date_of_issue",
            },
            {
              "dest": "date_of_expiry",
              "src": "date_of_expiry",
            },
            {
              "dest": "address_type",
              "src": "address_validation_type",
            },
            {
              "dest": "home_address_country",
              "src": "address_country_code",
            },
            {
              "dest": "home_address_full",
              "params": {
                "flag_equal": "full",
                "flag_name": "address_validation_type",
              },
              "src": "address",
            },
            {
              "dest": "home_address_address_1_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_address_1",
            },
            {
              "dest": "home_address_address_2_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_address_2",
            },
            {
              "dest": "home_address_city_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_city",
            },
            {
              "dest": "home_address_postal_code_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_postal_code",
            },
            {
              "dest": "home_address_zone_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_zone",
            },
            {
              "dest": "home_address_province",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "province",
            },
            {
              "dest": "home_address_district",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "district",
            },
            {
              "dest": "home_address_subdistrict",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "subdistrict",
            },
            {
              "dest": "home_address_address",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "address",
            },
            {
              "dest": "home_address_zipcode",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "zipcode",
            },
            {
              "dest": "gender",
              "src": "gender_code",
            },
            {
              "dest": "full_name_en_first_name",
              "src": "firstname_en",
            },
            {
              "dest": "full_name_en_last_name",
              "src": "lastname_en",
            },
          ],
          "builder": {
            "type": "ekyc_document_item_next",
          },
          "configs": {
            "check_ocr_fields": undefined,
            "check_warning": true,
            "enabled_vertical_experience": true,
          },
          "display": {
            "label": "",
          },
          "fields": {
            "country": "ekyc_document_country",
            "document_type": "ekyc_document_type",
            "ekyc_backcard_item": false,
            "preview": false,
          },
          "items": {
            "ekyc_document_country": {
              "builder": {
                "type": "country",
              },
              "display": {
                "label": "Country",
                "placeholder": "Select your country",
                "searchable": false,
              },
              "enum": undefined,
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "ekyc_document_country",
              "prefill": {
                "disabled": true,
                "run_only_empty": true,
              },
              "type": "CountrySelect",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_document_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "disabled_default_preview": true,
                "label": "Document type",
                "reverse_direction": true,
              },
              "enum": [
                {
                  "icon": "id-card",
                  "label": "ID Card",
                  "value": "front_card",
                },
                {
                  "icon": "passport",
                  "label": "Passport",
                  "value": "passport",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "ekyc_document_type",
              "prefill": {
                "value": "",
              },
              "type": "InputRadioStaticIcon",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "max_attempt_warning": {
            "allow_max_attempt_pass": false,
            "button": {
              "auto_redirect": false,
              "label": "Go to Home Page",
              "target": "/page/test",
              "visible": true,
            },
            "description": "Please contact the customer support.",
            "max_attempt_count": 5,
            "title": "ID verification failed",
          },
          "name": "ekyc_document",
          "selector_field": "ekyc_document_type",
          "type": "Ekyc.DocumentVNext",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "",
      "name": "ekyc_document_next",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
    "ekyc_document_next_2": {
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "date_of_birth": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "Date of birth",
            "year_format": "ce",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_birth",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {
                "years": -100,
              },
            },
            "reversed": {
              "year": true,
            },
            "to": {
              "exclusive": false,
              "today": {},
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "date_of_expiry": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "Date of expiry",
            "year_format": "ce",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_expiry",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {},
            },
            "reversed": {
              "year": false,
            },
            "to": {
              "exclusive": false,
              "today": {
                "years": 100,
              },
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "date_of_issue": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "Date of issue",
            "year_format": "ce",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_issue",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {
                "years": -100,
              },
            },
            "reversed": {
              "year": true,
            },
            "to": {
              "exclusive": false,
              "today": {},
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "document_numbers": {
          "builder": {
            "type": "document_numbers",
          },
          "display": {
            "hide_label": true,
            "label": "",
          },
          "fields": {
            "document_number": "document_number",
            "nid": "nid",
          },
          "items": {
            "document_number": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "หมายเลขเอกสาร",
                "mask": undefined,
                "placeholder": "หมายเลขเอกสาร",
              },
              "layout": "InputControl",
              "name": "document_number",
              "props": {
                "autocomplete": "off",
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_unless:ekyc_document_max_attempt,true",
              "visible": {
                "ekyc_document_country": "required|is:THA",
                "ekyc_document_type": "required|is:front_card",
              },
              "visible_flag_invert": true,
            },
            "nid": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "เลขบัตรประจำตัวประชาชน",
                "mask": "national_id",
                "placeholder": "เลขบัตรประจำตัวประชาชน",
              },
              "layout": "InputControl",
              "name": "nid",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if_condition:ekyc_document_country,=,THA,ekyc_document_country,=,front_card|regex:/^[0-9]{13}$/i|pass_checksum",
              "visible": {
                "ekyc_document_country": "required|is:THA",
                "ekyc_document_type": "required|is:front_card",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "document_numbers",
          "type": "Fieldset",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name": {
          "builder": {
            "type": "full_name",
          },
          "display": {
            "hide_label": true,
            "label": "Please enter your name",
          },
          "fields": {
            "first_name": "full_name_first_name",
            "full": "full_name_full",
            "last_name": "full_name_last_name",
            "middle_name": "full_name_middle_name",
            "prefix": false,
            "show_middle_name": "full_name_show_middle_name",
            "type": "full_name_type",
          },
          "items": {
            "full_name_first_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "First name",
                "mask": undefined,
                "placeholder": "Type your answer here",
              },
              "layout": "InputControl",
              "name": "full_name_first_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_unless": "Answer do not matches the selected format",
              },
              "validator_rule": "required_unless:full_name_type,full",
              "visible": {
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_full": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "ชื่อและนามสกุล",
                "mask": undefined,
                "placeholder": "กรุณากรอกชื่อและนามสกุล",
              },
              "layout": "InputControl",
              "name": "full_name_full",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_if": "Please Enter Your Full Name",
              },
              "validator_rule": "required_if:full_name_type,full",
              "visible": {
                "full_name_type": "required|is:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_last_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Last name",
                "mask": undefined,
                "placeholder": "Type your answer here",
              },
              "layout": "InputControl",
              "name": "full_name_last_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_unless": "Answer do not matches the selected format",
              },
              "validator_rule": "required_unless:full_name_type,full",
              "visible": {
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_middle_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Middle name",
                "mask": undefined,
                "placeholder": "Type your answer here",
              },
              "layout": "InputControl",
              "name": "full_name_middle_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_if_condition": "Answer do not matches the selected format",
              },
              "validator_rule": "required_if_condition:full_name_type,!=,full,full_name_show_middle_name,=,true",
              "visible": {
                "full_name_show_middle_name": "required:accepted",
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_show_middle_name": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Show Middle Name",
              },
              "enum": [
                {
                  "value": true,
                },
                {
                  "value": false,
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "full_name_show_middle_name",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_middle_name_flag": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Name Type",
              },
              "enum": [
                {
                  "value": "parts",
                },
                {
                  "value": "full",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "full_name_type",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_name_type": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "full_name",
          "type": "Fieldset",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name_en_first_name": {
          "builder": {
            "type": "data_field",
          },
          "display": {
            "hide_label": true,
            "label": "First name (EN)",
          },
          "layout": "DefaultWrapper",
          "name": "full_name_en_first_name",
          "type": "DataField",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name_en_last_name": {
          "builder": {
            "type": "data_field",
          },
          "display": {
            "hide_label": true,
            "label": "Last name (EN)",
          },
          "layout": "DefaultWrapper",
          "name": "full_name_en_last_name",
          "type": "DataField",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "gender": {
          "boolean": undefined,
          "builder": {
            "type": "gender",
          },
          "display": {
            "label": "Gender",
          },
          "enum": [
            {
              "label": "ชาย",
              "value": "M",
            },
            {
              "label": "หญิง",
              "value": "F",
            },
          ],
          "enum_presets": undefined,
          "layout": "InputControl",
          "name": "gender",
          "type": "SingleSelectButton",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "home_address": {
          "builder": {
            "type": "address",
          },
          "display": {
            "label": "Address",
          },
          "fields": {
            "address": "home_address_address",
            "address_1_common": "home_address_address_1_common",
            "address_2_common": "home_address_address_2_common",
            "address_reference": false,
            "city_common": "home_address_city_common",
            "country": "home_address_country",
            "district": "home_address_district",
            "full": "home_address_full",
            "postal_code_common": "home_address_postal_code_common",
            "province": "home_address_province",
            "subdistrict": "home_address_subdistrict",
            "type": "address_type",
            "zipcode": "home_address_zipcode",
            "zone_common": "home_address_zone_common",
          },
          "items": {
            "address_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Address Type",
              },
              "enum": [
                {
                  "value": "thai",
                },
                {
                  "value": "parts",
                },
                {
                  "value": "full",
                },
                {
                  "value": "empty",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "address_type",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_address_type": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "บ้านเลขที่, ซอย, หมู่, ถนน",
                "mask": undefined,
                "placeholder": "บ้านเลขที่, ซอย, หมู่, ถนน",
              },
              "layout": "InputControl",
              "name": "home_address_address",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_messages": {
                "required_if": "The Street Address field is required.",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address_1_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address 1",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_address_1_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address_2_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address 2",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_address_2_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_city_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "City",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_city_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_country": {
              "builder": {
                "type": "country",
              },
              "display": {
                "label": "ประเทศ",
                "placeholder": "ประเทศ",
                "searchable": true,
              },
              "enum": undefined,
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_country",
              "prefill": {
                "value": "THA",
              },
              "type": "CountrySelect",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "home_address_district": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "เขต/อำเภอ",
                "placeholder": "เลือกเขต/อำเภอ",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_district",
              "type": "Select",
              "validator_messages": {
                "required_if": "The District field is required.",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_full": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address (Full)",
                "mask": undefined,
                "placeholder": "กรุณากรอกที่อยู่",
              },
              "layout": "InputControl",
              "name": "home_address_full",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_messages": {
                "required_if": "Please enter your address",
              },
              "validator_rule": "required_if:address_type,full",
              "visible": {
                "address_type": "required|in:full",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_postal_code_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Postal code",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_postal_code_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_province": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "จังหวัด",
                "placeholder": "เลือกจังหวัด",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_province",
              "type": "Select",
              "validator_messages": {
                "required_if": "The Province field is required.",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_subdistrict": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "แขวง/ตำบล",
                "placeholder": "เลือกแขวง/ตำบล",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_subdistrict",
              "type": "Select",
              "validator_messages": {
                "required_if": "The Sub-district field is required.",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_zipcode": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "รหัสไปรษณีย์",
                "placeholder": "รหัสไปรษณีย์",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_zipcode",
              "type": "Select",
              "validator_messages": {
                "required_if": "The Postal / Zip Code field is required.",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_zone_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Zone",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_zone_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "home_address",
          "type": "AddressAutofill",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "name_prefix": {
          "boolean": undefined,
          "builder": {
            "type": "name_prefix",
          },
          "display": {
            "label": "คำนำหน้าชื่อ",
          },
          "enum": [
            {
              "label": "นาย",
              "value": "Mr.",
            },
            {
              "label": "นางสาว",
              "value": "Miss",
            },
            {
              "label": "นาง",
              "value": "Mrs.",
            },
          ],
          "enum_presets": undefined,
          "layout": "InputControl",
          "name": "name_prefix",
          "type": "SingleSelectButton",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "ID Document Details",
      "name": "ekyc_document_next_2",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
  },
  "styling": {
    "--form-section-header-align": "center",
    "--form-step-header-align": "center",
    "--form-step-header-size": "1.375rem",
  },
  "type": "Ekyc",
  "visible": undefined,
}
`;

exports[`CreatorStepDocumentNext (builder) > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "type": "ekyc_document_next",
  },
  "fields": {
    "date_of_birth": "date_of_birth",
    "date_of_expiry": "date_of_expiry",
    "date_of_issue": "date_of_issue",
    "document_numbers": "document_numbers",
    "ekyc_document_item": "ekyc_document",
    "full_name": "full_name",
    "full_name_en_first_name": "full_name_en_first_name",
    "full_name_en_last_name": "full_name_en_last_name",
    "gender": "gender",
    "home_address": "home_address",
    "laser_number": false,
    "name_prefix": "name_prefix",
  },
  "name": "ekyc_document_next",
  "sections": {
    "ekyc_document_next": {
      "description": "Description",
      "hide_description": true,
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "ekyc_document": {
          "accepted_countries": {
            "border_pass": [],
            "ci_passport": [],
            "driver_license": [],
            "front_card": [],
            "immigration_card": [],
            "monk_card": [],
            "other_document": [],
            "passport": [
              "GRC",
              "GRL",
              "GUM",
              "KHM",
              "GTM",
              "QAT",
              "GHA",
              "GAB",
              "CPV",
              "GUY",
              "GIN",
              "GNB",
              "CUW",
              "COG",
              "CRI",
              "COM",
              "KAZ",
              "KIR",
              "CUB",
              "KGZ",
              "KWT",
              "GEO",
              "JOR",
              "JAM",
              "DJI",
              "CHN",
              "TCD",
              "SXM",
              "CHL",
              "SMR",
              "WSM",
              "SAU",
              "ZWE",
              "SDN",
              "SSD",
              "SUR",
              "JPN",
              "TTO",
              "TON",
              "TLS",
              "TUR",
              "TUN",
              "TUV",
              "TJK",
              "VAT",
              "NOR",
              "NAM",
              "NRU",
              "NIC",
              "NZL",
              "BRA",
              "BWA",
              "BIH",
              "BGD",
              "BGR",
              "BRB",
              "BHR",
              "BHS",
              "BDI",
              "BFA",
              "PAK",
              "PAN",
              "PNG",
              "PRY",
              "PLW",
              "FRA",
              "MMR",
              "FJI",
              "FIN",
              "PHL",
              "BTN",
              "MNG",
              "MSR",
              "MNE",
              "MRT",
              "MLT",
              "MDV",
              "MDG",
              "MLI",
              "MAC",
              "MYS",
              "GIB",
              "UGA",
              "UKR",
              "RWA",
              "PSE",
              "BOL",
              "LUX",
              "LVA",
              "LIE",
              "LTU",
              "LBY",
              "VUT",
              "LKA",
              "SWZ",
              "CHE",
              "SWE",
              "RUS",
              "NPL",
              "ARE",
              "USA",
              "DOM",
              "GBR",
              "TZA",
              "COD",
              "LAO",
              "MDA",
              "MKD",
              "MWI",
              "SYR",
              "IRN",
              "CZE",
              "CAF",
              "ZAF",
              "VEN",
              "POL",
              "SGP",
              "ESP",
              "SVK",
              "SVN",
              "VGB",
              "MHL",
              "CYM",
              "TCA",
              "VIR",
              "SLB",
              "AUT",
              "AUS",
              "AND",
              "AFG",
              "ABW",
              "ARG",
              "AZE",
              "ARM",
              "ITA",
              "IND",
              "IDN",
              "IRQ",
              "ISR",
              "GNQ",
              "EGY",
              "UZB",
              "URY",
              "ASM",
              "HND",
              "HUN",
              "HKG",
              "GRD",
              "PRK",
              "KOR",
              "IMN",
              "KEN",
              "LCA",
              "VCT",
              "KNA",
              "SHN",
              "SRB",
              "STP",
              "SLE",
              "SYC",
              "SEN",
              "DNK",
              "TKM",
              "BRN",
              "NLD",
              "BEN",
              "BLR",
              "BLZ",
              "BEL",
              "BMU",
              "PER",
              "PRI",
              "MUS",
              "MEX",
              "DEU",
              "YEM",
              "LBN",
              "LSO",
              "ESH",
              "VNM",
              "ECU",
              "ETH",
              "ERI",
              "SLV",
              "EST",
              "HTI",
              "GMB",
              "CAN",
              "CMR",
              "ZMB",
              "MAF",
              "AIA",
              "AGO",
              "ATG",
              "DZA",
              "ALB",
              "CIV",
              "HRV",
              "COL",
              "SOM",
              "DMA",
              "TGO",
              "BES",
              "PRT",
              "MOZ",
              "MCO",
              "MAR",
              "ROU",
              "OMN",
              "CYP",
              "TWN",
              "THA",
              "NGA",
              "NER",
              "FSM",
              "LBR",
              "ISL",
              "IRL",
            ],
            "residence_permit": [],
            "thai_alien_card": [],
            "travel_document": [],
            "white_card": [],
            "work_permit_book": [],
            "work_permit_card": [],
          },
          "autofill_map": [
            {
              "dest": "nid",
              "src": "nid",
            },
            {
              "dest": "document_number",
              "src": "document_number",
            },
            {
              "dest": "name_prefix",
              "src": "title",
            },
            {
              "dest": "full_name_first_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "firstname",
            },
            {
              "dest": "full_name_last_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "lastname",
            },
            {
              "dest": "full_name_middle_name",
              "params": {
                "flag_equal": "parts",
                "flag_name": "name_type",
              },
              "src": "middlename",
            },
            {
              "dest": "full_name_full",
              "params": {
                "flag_equal": "full",
                "flag_name": "name_type",
              },
              "src": "fullname",
            },
            {
              "dest": "full_name_type",
              "src": "name_type",
            },
            {
              "dest": "full_name_show_middle_name",
              "src": "show_middle_name",
            },
            {
              "dest": "date_of_birth",
              "src": "date_of_birth",
            },
            {
              "dest": "date_of_issue",
              "src": "date_of_issue",
            },
            {
              "dest": "date_of_expiry",
              "src": "date_of_expiry",
            },
            {
              "dest": "address_type",
              "src": "address_validation_type",
            },
            {
              "dest": "home_address_country",
              "src": "address_country_code",
            },
            {
              "dest": "home_address_full",
              "params": {
                "flag_equal": "full",
                "flag_name": "address_validation_type",
              },
              "src": "address",
            },
            {
              "dest": "home_address_address_1_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_address_1",
            },
            {
              "dest": "home_address_address_2_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_address_2",
            },
            {
              "dest": "home_address_city_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_city",
            },
            {
              "dest": "home_address_postal_code_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_postal_code",
            },
            {
              "dest": "home_address_zone_common",
              "params": {
                "flag_equal": "parts",
                "flag_name": "address_validation_type",
              },
              "src": "address_zone",
            },
            {
              "dest": "home_address_province",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "province",
            },
            {
              "dest": "home_address_district",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "district",
            },
            {
              "dest": "home_address_subdistrict",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "subdistrict",
            },
            {
              "dest": "home_address_address",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "address",
            },
            {
              "dest": "home_address_zipcode",
              "params": {
                "flag_equal": "thai",
                "flag_name": "address_validation_type",
              },
              "src": "zipcode",
            },
            {
              "dest": "gender",
              "src": "gender_code",
            },
            {
              "dest": "full_name_en_first_name",
              "src": "firstname_en",
            },
            {
              "dest": "full_name_en_last_name",
              "src": "lastname_en",
            },
          ],
          "builder": {
            "type": "ekyc_document_item_next",
          },
          "configs": {
            "check_ocr_fields": undefined,
            "check_warning": true,
            "enabled_vertical_experience": true,
          },
          "display": {
            "label": "",
          },
          "fields": {
            "country": "ekyc_document_country",
            "document_type": "ekyc_document_type",
            "ekyc_backcard_item": false,
            "preview": false,
          },
          "items": {
            "ekyc_document_country": {
              "builder": {
                "type": "country",
              },
              "display": {
                "label": "ประเทศ",
                "placeholder": "กรุณาเลือกประเทศของคุณ",
                "searchable": false,
              },
              "enum": undefined,
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "ekyc_document_country",
              "prefill": {
                "disabled": true,
                "run_only_empty": true,
              },
              "type": "CountrySelect",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "ekyc_document_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "disabled_default_preview": true,
                "label": "ประเภทเอกสาร",
                "reverse_direction": true,
              },
              "enum": [
                {
                  "icon": "id-card",
                  "label": "บัตรประชาชน",
                  "value": "front_card",
                },
                {
                  "icon": "passport",
                  "label": "พาสปอร์ต",
                  "value": "passport",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "ekyc_document_type",
              "prefill": {
                "value": "",
              },
              "type": "InputRadioStaticIcon",
              "validator_rule": undefined,
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "max_attempt_warning": {
            "allow_max_attempt_pass": false,
            "button": {
              "auto_redirect": false,
              "label": "กลับหน้าหลัก",
              "target": "/page/test",
              "visible": true,
            },
            "description": "กรุณาติดต่อฝ่ายบริการลูกค้า",
            "max_attempt_count": 5,
            "title": "ไม่สามารถตรวจเอกสารยืนยันตัวตนได้",
          },
          "name": "ekyc_document",
          "selector_field": "ekyc_document_type",
          "type": "Ekyc.DocumentVNext",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "",
      "name": "ekyc_document_next",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
    "ekyc_document_next_2": {
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "date_of_birth": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "วันเกิด",
            "year_format": "be",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_birth",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {
                "years": -100,
              },
            },
            "reversed": {
              "year": true,
            },
            "to": {
              "exclusive": false,
              "today": {},
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "date_of_expiry": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "วันบัตรหมดอายุ",
            "year_format": "be",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_expiry",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {},
            },
            "reversed": {
              "year": false,
            },
            "to": {
              "exclusive": false,
              "today": {
                "years": 100,
              },
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "date_of_issue": {
          "builder": {
            "type": "date",
          },
          "display": {
            "date_order": "dmy",
            "label": "วันออกบัตร",
            "year_format": "be",
          },
          "fields": {
            "allow_no_day": undefined,
            "allow_no_month": undefined,
            "allow_no_year": undefined,
          },
          "layout": "InputControl",
          "name": "date_of_issue",
          "store_date_format": "yyyy-MM-dd",
          "type": "InputDate",
          "validator_rule": "required",
          "value_constraints": {
            "from": {
              "exclusive": false,
              "today": {
                "years": -100,
              },
            },
            "reversed": {
              "year": true,
            },
            "to": {
              "exclusive": false,
              "today": {},
            },
          },
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "document_numbers": {
          "builder": {
            "type": "document_numbers",
          },
          "display": {
            "hide_label": true,
            "label": "",
          },
          "fields": {
            "document_number": "document_number",
            "nid": "nid",
          },
          "items": {
            "document_number": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "หมายเลขเอกสาร",
                "mask": undefined,
                "placeholder": "หมายเลขเอกสาร",
              },
              "layout": "InputControl",
              "name": "document_number",
              "props": {
                "autocomplete": "off",
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_unless:ekyc_document_max_attempt,true",
              "visible": {
                "ekyc_document_country": "required|is:THA",
                "ekyc_document_type": "required|is:front_card",
              },
              "visible_flag_invert": true,
            },
            "nid": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "เลขบัตรประจำตัวประชาชน",
                "mask": "national_id",
                "placeholder": "เลขบัตรประจำตัวประชาชน",
              },
              "layout": "InputControl",
              "name": "nid",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if_condition:ekyc_document_country,=,THA,ekyc_document_country,=,front_card|regex:/^[0-9]{13}$/i|pass_checksum",
              "visible": {
                "ekyc_document_country": "required|is:THA",
                "ekyc_document_type": "required|is:front_card",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "document_numbers",
          "type": "Fieldset",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name": {
          "builder": {
            "type": "full_name",
          },
          "display": {
            "hide_label": true,
            "label": "Please enter your name",
          },
          "fields": {
            "first_name": "full_name_first_name",
            "full": "full_name_full",
            "last_name": "full_name_last_name",
            "middle_name": "full_name_middle_name",
            "prefix": false,
            "show_middle_name": "full_name_show_middle_name",
            "type": "full_name_type",
          },
          "items": {
            "full_name_first_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "ชื่อ",
                "mask": undefined,
                "placeholder": "กรอกคำตอบที่นี่",
              },
              "layout": "InputControl",
              "name": "full_name_first_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_unless": "กรุณากรอกข้อมูลให้ถูกต้อง",
              },
              "validator_rule": "required_unless:full_name_type,full",
              "visible": {
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_full": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "ชื่อและนามสกุล",
                "mask": undefined,
                "placeholder": "กรุณากรอกชื่อและนามสกุล",
              },
              "layout": "InputControl",
              "name": "full_name_full",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_if": "Please Enter Your Full Name",
              },
              "validator_rule": "required_if:full_name_type,full",
              "visible": {
                "full_name_type": "required|is:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_last_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "นามสกุล",
                "mask": undefined,
                "placeholder": "กรอกคำตอบที่นี่",
              },
              "layout": "InputControl",
              "name": "full_name_last_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_unless": "กรุณากรอกข้อมูลให้ถูกต้อง",
              },
              "validator_rule": "required_unless:full_name_type,full",
              "visible": {
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_middle_name": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "ชื่อกลาง",
                "mask": undefined,
                "placeholder": "กรอกคำตอบที่นี่",
              },
              "layout": "InputControl",
              "name": "full_name_middle_name",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_messages": {
                "required_if_condition": "กรุณากรอกข้อมูลให้ถูกต้อง",
              },
              "validator_rule": "required_if_condition:full_name_type,!=,full,full_name_show_middle_name,=,true",
              "visible": {
                "full_name_show_middle_name": "required:accepted",
                "full_name_type": "not_in:full",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_show_middle_name": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Show Middle Name",
              },
              "enum": [
                {
                  "value": true,
                },
                {
                  "value": false,
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "full_name_show_middle_name",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_middle_name_flag": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
            "full_name_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Name Type",
              },
              "enum": [
                {
                  "value": "parts",
                },
                {
                  "value": "full",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "full_name_type",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_name_type": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "full_name",
          "type": "Fieldset",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name_en_first_name": {
          "builder": {
            "type": "data_field",
          },
          "display": {
            "hide_label": true,
            "label": "First name (EN)",
          },
          "layout": "DefaultWrapper",
          "name": "full_name_en_first_name",
          "type": "DataField",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "full_name_en_last_name": {
          "builder": {
            "type": "data_field",
          },
          "display": {
            "hide_label": true,
            "label": "Last name (EN)",
          },
          "layout": "DefaultWrapper",
          "name": "full_name_en_last_name",
          "type": "DataField",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "gender": {
          "boolean": undefined,
          "builder": {
            "type": "gender",
          },
          "display": {
            "label": "เพศ",
          },
          "enum": [
            {
              "label": "ชาย",
              "value": "M",
            },
            {
              "label": "หญิง",
              "value": "F",
            },
          ],
          "enum_presets": undefined,
          "layout": "InputControl",
          "name": "gender",
          "type": "SingleSelectButton",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "home_address": {
          "builder": {
            "type": "address",
          },
          "display": {
            "label": "ที่อยู่",
          },
          "fields": {
            "address": "home_address_address",
            "address_1_common": "home_address_address_1_common",
            "address_2_common": "home_address_address_2_common",
            "address_reference": false,
            "city_common": "home_address_city_common",
            "country": "home_address_country",
            "district": "home_address_district",
            "full": "home_address_full",
            "postal_code_common": "home_address_postal_code_common",
            "province": "home_address_province",
            "subdistrict": "home_address_subdistrict",
            "type": "address_type",
            "zipcode": "home_address_zipcode",
            "zone_common": "home_address_zone_common",
          },
          "items": {
            "address_type": {
              "boolean": undefined,
              "builder": {
                "type": "choice",
              },
              "display": {
                "label": "Address Type",
              },
              "enum": [
                {
                  "value": "thai",
                },
                {
                  "value": "parts",
                },
                {
                  "value": "full",
                },
                {
                  "value": "empty",
                },
              ],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "address_type",
              "type": "InputRadio",
              "validator_rule": undefined,
              "visible": {
                "show_address_type": "required|accepted",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "บ้านเลขที่, ซอย, หมู่, ถนน",
                "mask": undefined,
                "placeholder": "บ้านเลขที่, ซอย, หมู่, ถนน",
              },
              "layout": "InputControl",
              "name": "home_address_address",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_messages": {
                "required_if": "กรุณาระบุ บ้านเลขที่, ซอย, หมู่, ถนน",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address_1_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address 1",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_address_1_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_address_2_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address 2",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_address_2_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_city_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "City",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_city_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_country": {
              "builder": {
                "type": "country",
              },
              "display": {
                "label": "ประเทศ",
                "placeholder": "ประเทศ",
                "searchable": true,
              },
              "enum": undefined,
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_country",
              "prefill": {
                "value": "THA",
              },
              "type": "CountrySelect",
              "validator_rule": "required",
              "visible": undefined,
              "visible_flag_invert": undefined,
            },
            "home_address_district": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "เขต/อำเภอ",
                "placeholder": "เลือกเขต/อำเภอ",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_district",
              "type": "Select",
              "validator_messages": {
                "required_if": "กรุณาเลือกเขต/อำเภอ",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_full": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Address (Full)",
                "mask": undefined,
                "placeholder": "กรุณากรอกที่อยู่",
              },
              "layout": "InputControl",
              "name": "home_address_full",
              "props": {
                "autocomplete": undefined,
                "maxlength": 1000,
                "type": undefined,
              },
              "type": "Textarea",
              "validator_messages": {
                "required_if": "กรุณากรอกที่อยู่",
              },
              "validator_rule": "required_if:address_type,full",
              "visible": {
                "address_type": "required|in:full",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_postal_code_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Postal code",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_postal_code_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_province": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "จังหวัด",
                "placeholder": "เลือกจังหวัด",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_province",
              "type": "Select",
              "validator_messages": {
                "required_if": "กรุณาเลือกจังหวัด",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_subdistrict": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "แขวง/ตำบล",
                "placeholder": "เลือกแขวง/ตำบล",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_subdistrict",
              "type": "Select",
              "validator_messages": {
                "required_if": "กรุณาเลือกแขวง/ตำบล",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_zipcode": {
              "builder": {
                "type": "dropdown",
              },
              "display": {
                "label": "รหัสไปรษณีย์",
                "placeholder": "รหัสไปรษณีย์",
              },
              "enum": [],
              "enum_presets": undefined,
              "layout": "InputControl",
              "name": "home_address_zipcode",
              "type": "Select",
              "validator_messages": {
                "required_if": "กรุณาเลือกรหัสไปรษณีย์",
              },
              "validator_rule": "required_if:address_type,thai",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
            "home_address_zone_common": {
              "builder": {
                "type": "short_long_answer",
              },
              "display": {
                "label": "Zone",
                "mask": undefined,
                "placeholder": "",
              },
              "layout": "InputControl",
              "name": "home_address_zone_common",
              "props": {
                "autocomplete": undefined,
                "maxlength": 280,
                "type": undefined,
              },
              "type": "InputText",
              "validator_rule": "required_if:address_type,parts",
              "visible": {
                "address_type": "not_in:full,empty",
              },
              "visible_flag_invert": undefined,
            },
          },
          "layout": "DefaultWrapper",
          "name": "home_address",
          "type": "AddressAutofill",
          "validator_rule": undefined,
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
        "name_prefix": {
          "boolean": undefined,
          "builder": {
            "type": "name_prefix",
          },
          "display": {
            "label": "คำนำหน้าชื่อ",
          },
          "enum": [
            {
              "label": "นาย",
              "value": "Mr.",
            },
            {
              "label": "นางสาว",
              "value": "Miss",
            },
            {
              "label": "นาง",
              "value": "Mrs.",
            },
          ],
          "enum_presets": undefined,
          "layout": "InputControl",
          "name": "name_prefix",
          "type": "SingleSelectButton",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "รายละเอียดเอกสารยืนยันตัวตน",
      "name": "ekyc_document_next_2",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
  },
  "styling": {
    "--form-section-header-align": "center",
    "--form-step-header-align": "center",
    "--form-step-header-size": "1.375rem",
  },
  "type": "Ekyc",
  "visible": undefined,
}
`;
