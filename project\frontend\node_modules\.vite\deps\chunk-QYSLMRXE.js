import {
  require_overRest,
  require_setToString
} from "./chunk-6U5UHEYX.js";
import {
  require_identity
} from "./chunk-64Z5HK43.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseRest.js
var require_baseRest = __commonJS({
  "node_modules/lodash/_baseRest.js"(exports, module) {
    var identity = require_identity();
    var overRest = require_overRest();
    var setToString = require_setToString();
    function baseRest(func, start) {
      return setToString(overRest(func, start, identity), func + "");
    }
    module.exports = baseRest;
  }
});

export {
  require_baseRest
};
//# sourceMappingURL=chunk-QYSLMRXE.js.map
