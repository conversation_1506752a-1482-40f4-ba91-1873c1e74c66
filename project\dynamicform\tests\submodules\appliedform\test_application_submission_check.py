from django.test import override_settings
from rest_framework import status
from rest_framework.test import APIRequestFactory
from unittest import mock
from dynamicform.models import Form, AppliedForm
from dynamicform.submodules.application.models import Application
from dynamicform.tests.case import TestDatabaseTestCase
from dynamicform.submodules.appliedform.views import AppliedFormViewSet


@mock.patch('dynamicform.schema.dynamicform.components.component.Component.user_log', mock.Mock())
@mock.patch('dynamicform.schema.dynamicform.components.component.Component.user_logs', mock.Mock())
@mock.patch('dynamicform.submodules.appliedform.dynamicform.info.DynamicformInfo.set_up_crequest', mock.Mock())
@override_settings(ENABLE_CREDIT_SYSTEM=False)
class ApplicationSubmissionCheckTest(TestDatabaseTestCase):
    def setUp(self):
        super(ApplicationSubmissionCheckTest, self).setUp()
        self.form = Form(
            name='test',
            slug='slug-test-a',
            frontend_schema={
                'steps': {
                    'step1': {
                        'sections': {
                            's1': {
                                'items': {
                                    'item_1': {
                                        'name': 'item1',
                                        'type': 'inputText',
                                    },
                                }
                            },
                        }
                    },
                }
            },
            backend_schema={
                'first_step_section': ['step1', 's1']
            }
        )
        self.form.save()
        
        # Create an application
        self.application = Application.objects.create(slug='test-app-slug')
        
        # Create an applied form
        self.applied_form = AppliedForm(
            form=self.form,
            slug='test-applied-form-slug',
            step='step1',
            section='s1'
        )
        self.applied_form.save()
        
        # Link the applied form to the application
        self.application.add_applied_form(applied_form=self.applied_form)
        
        self.factory = APIRequestFactory()

    def test_info_returns_302_when_application_submitted_via_api(self):
        """Test that info endpoint returns 302 when application is submitted via API check"""
        # Mock the API check to return True (submitted)
        with mock.patch.object(self.applied_form, 'check_application_submission_status_via_api', return_value=True):
            view = AppliedFormViewSet.as_view({'get': 'info'})
            request = self.factory.get('')
            
            response = view(
                request,
                form_slug=self.form.slug,
                slug=self.applied_form.slug
            )
            
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.content.decode(), "Not found application")
            self.assertEqual(response['Location'], '/')

    def test_save_returns_302_when_application_submitted_via_api(self):
        """Test that save endpoint returns 302 when application is submitted via API check"""
        # Mock the API check to return True (submitted)
        with mock.patch.object(self.applied_form, 'check_application_submission_status_via_api', return_value=True):
            view = AppliedFormViewSet.as_view({'post': 'save'})
            request = self.factory.post('', {
                'answers': {'item1': 'test value'},
                'step': 'step1',
                'section': 's1'
            }, format='json')
            
            response = view(
                request,
                form_slug=self.form.slug,
                slug=self.applied_form.slug
            )
            
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.content.decode(), "Not found application")
            self.assertEqual(response['Location'], '/')

    def test_submit_returns_302_when_application_submitted_via_api(self):
        """Test that submit endpoint returns 302 when application is submitted via API check"""
        # Mock the API check to return True (submitted)
        with mock.patch.object(self.applied_form, 'check_application_submission_status_via_api', return_value=True):
            view = AppliedFormViewSet.as_view({'post': 'submit'})
            request = self.factory.post('', {
                'answers': {'item1': 'test value'},
                'step': 'step1',
                'section': 's1'
            }, format='json')
            
            response = view(
                request,
                form_slug=self.form.slug,
                slug=self.applied_form.slug
            )
            
            self.assertEqual(response.status_code, 302)
            self.assertEqual(response.content.decode(), "Not found application")
            self.assertEqual(response['Location'], '/')

    def test_info_works_normally_when_application_not_submitted(self):
        """Test that info endpoint works normally when application is not submitted"""
        # Mock the API check to return False (not submitted)
        with mock.patch.object(self.applied_form, 'check_application_submission_status_via_api', return_value=False):
            with mock.patch.object(self.applied_form, 'can_view_info', return_value=True):
                view = AppliedFormViewSet.as_view({'get': 'info'})
                request = self.factory.get('')
                
                response = view(
                    request,
                    form_slug=self.form.slug,
                    slug=self.applied_form.slug
                )
                
                # Should return 200 and normal response, not 302
                self.assertEqual(response.status_code, 200)
                self.assertNotEqual(response.content.decode(), "Not found application")

    def test_check_application_submission_status_via_api_method(self):
        """Test the check_application_submission_status_via_api method directly"""
        # Test when application is not submitted
        result = self.applied_form.check_application_submission_status_via_api()
        self.assertFalse(result)
        
        # Test when application is submitted (set submitted_at)
        from django.utils import timezone
        self.application.submitted_at = timezone.now()
        self.application.save()
        
        result = self.applied_form.check_application_submission_status_via_api()
        self.assertTrue(result)

    def test_check_application_submission_status_via_api_fallback(self):
        """Test that the method falls back to local check when API fails"""
        # Test when there's no application
        applied_form_no_app = AppliedForm(
            form=self.form,
            slug='test-no-app-slug',
            step='step1',
            section='s1'
        )
        applied_form_no_app.save()
        
        result = applied_form_no_app.check_application_submission_status_via_api()
        self.assertFalse(result)  # Should fallback to is_submitted() which returns False
        
        # Set submitted_at and test again
        from django.utils import timezone
        applied_form_no_app.submitted_at = timezone.now()
        applied_form_no_app.save()
        
        result = applied_form_no_app.check_application_submission_status_via_api()
        self.assertTrue(result)  # Should fallback to is_submitted() which returns True
