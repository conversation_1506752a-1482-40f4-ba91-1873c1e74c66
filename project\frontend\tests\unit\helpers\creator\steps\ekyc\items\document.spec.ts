import { beforeEach, describe, expect, test } from 'vitest';

import { CreatorSchema } from '@/helpers/creator/schema';
import { CreatorStepDocument } from '@/helpers/creator/steps/ekyc/document';
import { CreatorItemDocument } from '@/helpers/creator/steps/ekyc/items/document';

export const AUTOFILL_ITEM_FIELDS = {
  document_numbers: ['nid', 'document_number'],
  name_prefix: ['title'],
  full_name: ['firstname', 'lastname', 'fullname', 'name_type', 'middlename', 'show_middle_name'],
  date_of_birth: ['date_of_birth'],
  date_of_issue: ['date_of_issue'],
  date_of_expiry: ['date_of_expiry'],
  home_address: [
    'address_validation_type',
    'address_country_code',
    'address',
    'address_address_1',
    'address_address_2',
    'address_city',
    'address_postal_code',
    'address_zone',
    'province',
    'district',
    'subdistrict',
    'address',
    'zipcode',
  ],
  gender: ['gender_code'],
  full_name_en_first_name: ['firstname_en'],
  full_name_en_last_name: ['lastname_en'],
} as const;

export const AUTOFILL_ITEM_NAMES = Object.keys(
  AUTOFILL_ITEM_FIELDS,
) as (keyof typeof AUTOFILL_ITEM_FIELDS)[];

describe('CreatorItemDocument (builder)', () => {
  let step: CreatorStepDocument;
  let item: CreatorItemDocument;

  beforeEach(() => {
    const createSchema = new CreatorSchema('test');
    step = new CreatorStepDocument();
    step.creatorSchema = createSchema;
    step.setSchema({} as any);
    item = step.fields.ekyc_document_item.item;
    item.creatorSchema = createSchema;
  });

  test('must have correct static class info', () => {
    expect(item.TYPE_ID).toBe('ekyc_document_item');
  });

  test('must have correct default values', () => {
    item.setSchema({} as any);

    expect(item.fields.country.enabled).toBe(true);
    expect(item.fields.document_type.enabled).toBe(true);
    expect(item.fields.preview.enabled).toBe(false);

    AUTOFILL_ITEM_NAMES.forEach(itemName => {
      expect(item.refs[itemName].enabled).toBe(true);
      expect(item.lockedFieldInfo[itemName]).toBe(false);
    });
  });

  test('must have correct constant in schema', () => {
    item.syncSchema();

    expect(item.schema.type).toBe('Ekyc.Document');
    expect(item.schema.layout).toBe('DefaultWrapper');
    expect(item.schema.include_nfc).toBeUndefined();
    expect(item.schema.configs.check_ocr_fields).toBeUndefined();
  });

  test.each(AUTOFILL_ITEM_NAMES)(`must have correct schema when enable/disable "%s"`, itemName => {
    item.syncSchema();
    AUTOFILL_ITEM_FIELDS[itemName].forEach(autofillName => {
      expect(item.schema.autofill_map.find(a => a.src === autofillName)).toBeDefined();
    });

    item.refs[itemName].enabled = false;
    item.syncSchema();

    AUTOFILL_ITEM_FIELDS[itemName].forEach(autofillName => {
      expect(item.schema.autofill_map.find(a => a.src === autofillName)).toBeUndefined();
    });
  });

  test('must have correct nfc enabled', () => {
    item.includeNfc = true;
    item.syncSchema();

    expect(item.schema.include_nfc).toBe(true);
    expect(item.schema.configs.check_ocr_fields).toEqual([
      {
        field: 'document_number',
      },
      {
        field: 'date_of_birth',
      },
      {
        field: 'date_of_expiry',
      },
    ]);
  });
});
