// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CreatorItemConsent (builder) > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "type": "consent",
  },
  "content": "",
  "content_styling": {
    "background_color": {
      "enabled": false,
      "value": "#FFF",
    },
    "border_color": {
      "enabled": false,
      "value": "#EEEEF0",
    },
  },
  "display": {
    "label": "",
  },
  "enable_scroll_bottom": false,
  "fields": {
    "content": "consent_content",
    "date": "consent_date",
    "version": "consent_version",
  },
  "items": {
    "consent_checkbox_1": {
      "boolean": true,
      "display": {
        "error_template": {
          "item": false,
        },
        "hide_label": true,
        "label": "I have read and agreed to the agreement",
      },
      "enum": [
        {
          "label": "I have read and agreed to the agreement",
          "value": true,
        },
      ],
      "layout": "InputControl",
      "name": "consent_checkbox_1",
      "type": "InputCheckbox",
      "validator_rule": "required",
    },
    "consent_content": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent content",
      },
      "layout": "DefaultWrapper",
      "name": "consent_content",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
    "consent_date": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent date",
      },
      "layout": "DefaultWrapper",
      "name": "consent_date",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
    "consent_version": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent version",
      },
      "layout": "DefaultWrapper",
      "name": "consent_version",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
  },
  "layout": "DefaultWrapper",
  "name": "consent",
  "title_styling": {
    "color": {
      "enabled": true,
      "value": "#000",
    },
  },
  "type": "Consent",
  "validator_rule": undefined,
  "version": "",
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;

exports[`CreatorItemConsent (builder) > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "type": "consent",
  },
  "content": "",
  "content_styling": {
    "background_color": {
      "enabled": false,
      "value": "#FFF",
    },
    "border_color": {
      "enabled": false,
      "value": "#EEEEF0",
    },
  },
  "display": {
    "label": "",
  },
  "enable_scroll_bottom": false,
  "fields": {
    "content": "consent_content",
    "date": "consent_date",
    "version": "consent_version",
  },
  "items": {
    "consent_checkbox_1": {
      "boolean": true,
      "display": {
        "error_template": {
          "item": false,
        },
        "hide_label": true,
        "label": "I have read and agreed to the agreement",
      },
      "enum": [
        {
          "label": "I have read and agreed to the agreement",
          "value": true,
        },
      ],
      "layout": "InputControl",
      "name": "consent_checkbox_1",
      "type": "InputCheckbox",
      "validator_rule": "required",
    },
    "consent_content": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent content",
      },
      "layout": "DefaultWrapper",
      "name": "consent_content",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
    "consent_date": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent date",
      },
      "layout": "DefaultWrapper",
      "name": "consent_date",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
    "consent_version": {
      "builder": {
        "type": "data_field",
      },
      "display": {
        "label": "Consent version",
      },
      "layout": "DefaultWrapper",
      "name": "consent_version",
      "type": "DataField",
      "validator_rule": undefined,
      "visible": true,
      "visible_flag_invert": undefined,
    },
  },
  "layout": "DefaultWrapper",
  "name": "consent",
  "title_styling": {
    "color": {
      "enabled": true,
      "value": "#000",
    },
  },
  "type": "Consent",
  "validator_rule": undefined,
  "version": "",
  "visible": undefined,
  "visible_flag_invert": undefined,
}
`;
