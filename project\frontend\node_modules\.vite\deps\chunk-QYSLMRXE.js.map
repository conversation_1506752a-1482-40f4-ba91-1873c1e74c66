{"version": 3, "sources": ["../../lodash/_baseRest.js"], "sourcesContent": ["var identity = require('./identity'),\n    overRest = require('./_overRest'),\n    setToString = require('./_setToString');\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  return setToString(overRest(func, start, identity), func + '');\n}\n\nmodule.exports = baseRest;\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,WAAW;AADf,QAEI,cAAc;AAUlB,aAAS,SAAS,MAAM,OAAO;AAC7B,aAAO,YAAY,SAAS,MAAM,OAAO,QAAQ,GAAG,OAAO,EAAE;AAAA,IAC/D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}