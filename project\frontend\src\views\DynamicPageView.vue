<script setup lang="ts">
import get from 'lodash/get';

import DynamicFormDebugOverlay from '@core/components/DynamicFormDebugOverlay/DynamicFormDebugOverlay.vue';
import DynamicComponent from '@core/components/dynamic-form/DynamicComponent.vue';
import RootStyleInjector from '@core/components/dynamic-form/RootStyleInjector.vue';
import {
  initAllowFormulaWithFormSetting,
  initInputDataWithFormSetting,
  overrideDocumentWithFormSetting,
  useSchemaConfigFormSettings,
} from '@core/composables/dynamic-form-settings/use-schema-config-form-settings';
import { useDynamicEvent } from '@core/composables/use-dynamic-event';
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import { useDynamicFormTranslation } from '@core/composables/use-dynamic-form-translation';
import { processSection } from '@core/helpers/schema-processor';

import { Sentry } from '@helpers/plugins/sentry';

import PopupCrossDeviceReturn from '@/components/common/PopupCrossDeviceReturn.vue';
import { TrackEventOptions, useGoogleTagManager } from '@/composables/use-google-tag-manager';
import { useRouter } from '@/composables/use-router';
import { defaultSchemas, usePageStore } from '@/store/page';

const LOG_PREFIX = ['%c[DynamicPage]', 'background: #f37; color: #fff; border: 1px solid #f88;'];

const props = defineProps({
  lang: {
    required: true,
    type: String,
  },
  pageName: {
    required: true,
    type: String,
  },
  formSlug: {
    required: false,
    type: String,
    default: null,
  },
  appliedFormSlug: {
    required: false,
    type: String,
    default: null,
  },
  data: {
    required: false,
    type: Object,
    default: null,
  },
});

const { sendGtmEvent } = useGoogleTagManager();

const { createFormInstance, mainInstance } = useDynamicFormApp();
const pageStore = usePageStore();
const { router } = useRouter();

const isLoading = ref(false);
const isCrossDevice = ref(false);

const dynamicFormInstance = createFormInstance();

const translationLogic = useDynamicFormTranslation(
  dynamicFormInstance,
  `/:lang/api/pages/${props.pageName}/trans/`,
);

const { initListeningEvents, setTrigger, setFlag, unsetFlag, resetEvents } = useDynamicEvent(
  'app.form',
  dynamicFormInstance.id,
);

const localFormSlug = ref(props.formSlug);

const { schemaConfigSetting } = useSchemaConfigFormSettings(localFormSlug);
const formStyling = computed(() => ({
  ...schemaConfigSetting.value?.styling,
  ...pageSchema.value?.styling,
}));
const pageSchema = computed(() => dynamicFormInstance.formSchema as any as Types.ISchemaPage);
const showBranding = computed(() => schemaConfigSetting.value?.configs?.branding?.show_branding);

provide('dynamicFormInstance', dynamicFormInstance);
provide('id', dynamicFormInstance.id);

function sendGtmEventWithProps(additional: TrackEventOptions | Record<string, string>) {
  sendGtmEvent({
    formSlug: props.formSlug,
    appliedFormSlug: props.appliedFormSlug,
    lang: props.lang,
    pageName: props.pageName,
    ...additional,
  });
}

function processPageSchema(schema: Types.ISchemaPage) {
  if (!schema && defaultSchemas[props.pageName]) {
    console.log(...LOG_PREFIX, 'load from default');
    schema = defaultSchemas[props.pageName];
  }

  if (!schema) {
    throw new Error(`Page not found '${props.pageName}'`);
  }

  schema = processSection(schema);

  dynamicFormInstance.formSchema = schema as any; // Has to set to make trans work
  if (schema.data) {
    dynamicFormInstance.initializeInputData({ ...schema.data });
  }
  if (schema.form) {
    localFormSlug.value = schema.form;
    dynamicFormInstance.appliedFormSlug = props.appliedFormSlug ?? '';
    dynamicFormInstance.formSlug = localFormSlug.value;
  }
  if (schema.trans) {
    translationLogic.translationCache[props.lang] = schema.trans;
  }
  if (schema.schema_config) {
    const schemaConfigs: Types.ConfigPartForSchema = {
      ...schema.schema_config,
      styling: {
        ...schema.schema_config?.styling,
        ...schema.styling,
      },
      configs: {
        ...schema.schema_config?.configs,
        ...schema.configs,
      },
    };
    schemaConfigSetting.value = schemaConfigs;
    overrideDocumentWithFormSetting();
    initAllowFormulaWithFormSetting();
    initInputDataWithFormSetting(dynamicFormInstance);
  }
}

async function init() {
  try {
    isLoading.value = true;

    const pageName = props.pageName;
    const formSlug = props.formSlug;
    const lang = props.lang;
    const appliedFormSlug = props.appliedFormSlug;
    const data = props.data;

    // Form Instance
    dynamicFormInstance.formControl.vueElement = getCurrentInstance().proxy;
    mainInstance.value = dynamicFormInstance;
    if (data) {
      dynamicFormInstance.initializeInputData({ ...data });
    }

    // Cross device logic
    isCrossDevice.value = !!+router.currentRoute.query.cross_device;
    if (isCrossDevice.value) {
      dynamicFormInstance.setFormData({ field: '_cross_device', value: true });
    }

    // Send gtag event
    sendGtmEventWithProps({
      event: 'form_started',
      action: 'started',
      category: 'form_control',
      label: `${lang}/${formSlug}/${appliedFormSlug} started`,
      noninteraction: true,
    });

    // Sentry
    Sentry.setContext('page', {
      pageName,
      lang,
      formSlug,
      appliedFormSlug,
    });

    Sentry.setTags({
      pageName,
      lang,
      formSlug,
      appliedFormSlug,
    });

    // Load schema
    let schema: Types.ISchemaPage;
    pageStore.loadFromHeader();

    if (pageStore.schemas[pageName]) {
      console.log(...LOG_PREFIX, 'load from header');
      schema = pageStore.schemas[pageName];
    }

    if (!schema) {
      console.log(...LOG_PREFIX, 'load from api');
      try {
        schema = await pageStore.getPage({
          name: pageName,
          appliedFormSlug,
        });
      } catch {
        if (appliedFormSlug) {
          router.push({
            name: 'error.applicationNotFound',
            params: {
              lang,
            },
          });
          return;
        }
      }
    }

    processPageSchema(schema);
  } catch (err) {
    console.error(err);
    router.replace({
      name: 'error.notFound',
      params: {
        lang: props.lang,
      },
    });
  } finally {
    isLoading.value = false;
  }
}

watch(
  () => dynamicFormInstance.formSchema,
  schema => {
    // Set dynamic events
    resetEvents();
    initListeningEvents(schema.events || []);
    setTrigger('mounted');
    setFlag('active');
  },
);
watch(isLoading, val => {
  if (val) {
    window.df_loading_list?.add('page');
  } else {
    window.df_loading_list?.remove('page');
  }
});

onMounted(() => {
  init();
});

onUnmounted(() => {
  setTrigger('unmounted');
  unsetFlag('active');
});
</script>

<template>
  <div class="ok-dy__dynamic_page">
    <span class="dynamic-form__header">{{ pageSchema.label }}</span>
    <div class="dynamic-form__subheader">{{ pageSchema.subheading }}</div>
    <DynamicFormDebugOverlay />
    <RootStyleInjector :styling="formStyling" />
    <PopupCrossDeviceReturn v-if="isCrossDevice" />
    <div v-else-if="isLoading"></div>
    <div v-else class="content" :class="get(pageSchema, 'builder.type', '')">
      <DynamicComponent
        v-for="(element, field) in pageSchema.items"
        :key="field"
        :ref="field"
        :element="element"
      />
    </div>
    <div v-if="showBranding" class="branding-content container">
      <img
        class="branding-img"
        src="https://cdn.uppass.io/logos/powered-by-uppass.svg"
        alt="Powered by Uppass"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.branding-content {
  position: fixed;
  bottom: 72px;
  margin: 0 auto;
  padding: 5px;
  width: 90%;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  left: 0;
  right: 0;
}
.branding-img {
  backdrop-filter: blur(10px);
}
</style>
