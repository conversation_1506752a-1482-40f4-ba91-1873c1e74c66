export async function changeHeicType(
  file: File,
  { ext = '.jpg', type = 'image/jpeg', quality = 1 },
) {
  try {
    const newFileName = file.name.replace(/\.heic|\.heif/i, ext);
    if (file.name !== newFileName) {
      const heic2any = await import(/* webpackChunkName: "heic2any" */ 'heic2any').then(
        module => module.default,
      );
      const convertedBlob = await heic2any({ blob: file, toType: type, quality });
      return { converted: true, file: new File([convertedBlob as Blob], newFileName, { type }) };
    }
  } catch (err) {
    console.error(err);
  }
  return { converted: false, file };
}

function getImageFromFile(file: File) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      resolve(img);
    };

    img.onerror = () => {
      URL.revokeObjectURL(img.src); // Clean up the object URL
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
}

type ResizeOptions = {
  maxHeight?: number;
  maxWidth?: number;
  minWidth?: number;
  maxAnySide?: number;
  quality?: number;
};

export async function resizeImageFileToCap(file: File, resizeOptions: ResizeOptions) {
  try {
    if (file.type.includes('image')) {
      // load image
      const img = await getImageFromFile(file);

      // get resize w/h
      const { resizedWidth, resizedHeight } = getResizedResolution(
        { inputWidth: img.width, inputHeight: img.height },
        resizeOptions,
      );

      // draw resized canvas
      const canvas = resizeCanvas(img, { width: resizedWidth, height: resizedHeight });

      // Convert canvas to Blob
      const blob = await new Promise<Blob>(resolve => {
        canvas.toBlob(resolve, file.type, resizeOptions?.quality);
      });

      // Create new File object
      const resizedFile = new File([blob], file.name, {
        type: file.type,
        lastModified: Date.now(),
      });

      return { converted: true, file: resizedFile, resizedWidth, resizedHeight, blob };
    }
  } catch (err) {
    console.error(err);
  }
  return { converted: true, file };
}

export function resizeCanvas(canvasSource: CanvasImageSource, { width = 1920, height = 1080 }) {
  const outputCanvas = document.createElement('canvas');
  outputCanvas.width = width;
  outputCanvas.height = height;

  const ctx = outputCanvas.getContext('2d');
  ctx.drawImage(canvasSource, 0, 0, width, height); // Full Size
  return outputCanvas;
}

export function getResizedResolution(
  { inputWidth = 0, inputHeight = 0 },
  { maxHeight = 4096, maxWidth = 4096, minWidth = 0, maxAnySide = 4096 }: ResizeOptions,
) {
  let resizedWidth = inputWidth;
  let resizedHeight = inputHeight;

  if (maxAnySide) {
    if (resizedHeight > maxAnySide) {
      const ratio = resizedWidth / resizedHeight;
      resizedHeight = maxAnySide;
      resizedWidth = resizedHeight * ratio;
    }
    if (resizedWidth > maxAnySide) {
      const ratio = resizedHeight / resizedWidth;
      resizedWidth = maxAnySide;
      resizedHeight = resizedWidth * ratio;
    }
  }

  if (maxHeight && resizedHeight > maxHeight) {
    const ratio = resizedWidth / resizedHeight;
    resizedHeight = maxHeight;
    resizedWidth = resizedHeight * ratio;
  }

  if (maxWidth && resizedWidth > maxWidth) {
    const ratio = resizedHeight / resizedWidth;
    resizedWidth = maxWidth;
    resizedHeight = resizedWidth * ratio;
  }

  if (minWidth && resizedWidth < minWidth) {
    const ratio = resizedHeight / resizedWidth;
    resizedWidth = minWidth;
    resizedHeight = resizedWidth * ratio;
  }

  return {
    resizedWidth,
    resizedHeight,
  };
}

export default { changeHeicType, resizeImageFileToCap };
