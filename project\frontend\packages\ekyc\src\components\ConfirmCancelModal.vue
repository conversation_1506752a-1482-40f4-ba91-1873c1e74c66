<template>
  <div class="modal ekyc-confirm-cancel" :class="{ 'is-active': isActive }">
    <div class="modal-background" />
    <div class="modal-content modal-card">
      <section class="modal-card-body has-text-centered">
        {{ question }}
      </section>
      <footer class="modal-card-foot">
        <button class="button is-text" @click.prevent="emit('no')">
          {{ t('ekyc.recorder.not_cancel') }}
        </button>
        <button class="button is-text" @click.prevent="emit('yes')">
          {{ t('ekyc.recorder.cancel') }}
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

defineProps({
  question: {
    type: String as () => string | any,
    required: true,
  },
  isActive: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['yes', 'no']);

const { t } = useI18n();
</script>
