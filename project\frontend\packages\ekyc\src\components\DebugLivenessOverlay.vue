<script setup lang="ts">
import { DrawingUtils, FaceLandmarker } from '@mediapipe/tasks-vision';

import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useFaceAction } from '@ekyc/composables/liveness/use-face-action';
import { useHistory } from '@ekyc/composables/liveness/use-history';
import { useRecordedData } from '@ekyc/composables/liveness/use-recorded-data';
import { playAnimation } from '@ekyc/helpers/animations';
import { buildThreshold } from '@ekyc/helpers/face-action-checkers/face-action-checker-3d';
import type { FaceLandmarks68 } from '@ekyc/helpers/face-api-proxies/classes/FaceLandmarks68';
import { getAlignedScaledPointsFromLandmark } from '@ekyc/helpers/face-size-checker';

const { debugInfo, freezeTime, dontPass } = useDebug();
const { recordedDataList } = useRecordedData();
const { currentHistory } = useHistory();

const debugCanvas = ref<HTMLCanvasElement>();
const showBlendshapes = ref(false);
const showingMoreInfo = ref(false);
const showingActions = ref(false);
const enableBlendshapeSorting = ref(true);
const selectedAnimation = ref('startInstruction');
const animationOptions = [
  'startInstruction',
  'manualSnap',
  'retryLiveness',
  'startChecking',
  'progressLiveness',
  'successIndicator',
  'failIndicator',
  'warnIndicator',
  'showOverlay',
  'progressUpload',
];

const { currentAction, actionProgress } = useFaceAction();

const minYaw = ref(0);
const maxYaw = ref(180);
const currentYaw = computed(() => debugInfo?.actionLogs?.[0]?.find(d => d.method === 'angle')?.yaw);

const minPitch = ref(0);
const maxPitch = ref(180);
const currentPitch = computed(
  () => debugInfo?.actionLogs?.[0]?.find(d => d.method === 'angle')?.pitch,
);

// Add computed property for blendshapes
const blendshapes = computed(() => {
  const results = debugInfo.faceResults || [];
  if (!results.length || !results[0]?.blendshape) return [];

  return Object.entries(results[0].blendshape).map(([name, score]) => ({ name, score }));
});

// Sort blendshapes by score for better visualization
const sortedBlendshapes = computed(() => {
  if (!blendshapes.value.length) return [];
  if (enableBlendshapeSorting.value) {
    return [...blendshapes.value].sort((a, b) => b.score - a.score);
  }
  return blendshapes.value;
});

// Get color based on blendshape score
function getBlendshapeColor(score) {
  if (score > 0.8) return '#4CAF50'; // Green
  if (score > 0.5) return '#FFC107'; // Yellow
  if (score > 0.2) return '#FF9800'; // Orange
  return '#F44336'; // Red
}

function drawLine({ x1 = 0, y1 = 0, x2 = 0, y2 = 0, color = 'orange', text = '' }) {
  const ctx = debugCanvas.value.getContext('2d');
  ctx.strokeStyle = color;
  ctx.lineWidth = 10;
  ctx.beginPath();
  ctx.moveTo(x1, y1);
  ctx.lineTo(x2, y2);
  ctx.stroke();
  if (text) {
    ctx.fillText(text, x2, y2);
  }
}

function drawRect(
  ctx: CanvasRenderingContext2D,
  rect: { x: number; y: number; width: number; height: number },
  color = 'grey',
  lineWidth = 1,
) {
  ctx.strokeStyle = color;
  ctx.fillStyle = '';

  ctx.lineWidth = lineWidth;

  ctx.beginPath();
  ctx.rect(rect.x, rect.y, rect.width, rect.height);
  ctx.stroke();
}

const onFaceResultsChange = () => {
  const faceResults = debugInfo.faceResults;
  const rects = debugInfo.faceMaskRect;
  const activeRect = rects?.active_rect;
  // console.log('[DEBUG] faceResults', faceResults);
  const faces = faceResults
    .filter(f => f.landmarks)
    .map(
      f => getAlignedScaledPointsFromLandmark(f.landmarks as FaceLandmarks68, activeRect).points,
    );

  const colors = ['#4364e8', '#ff0000', '#00ff00', '#ffff00'];
  faces.forEach((points, faceI) => {
    const ctx = debugCanvas.value.getContext('2d');

    const color = colors[faceI];
    const hasValue = ([x, y]) => x !== 0 || y !== 0;
    const reverseX = ([x, y]): [number, number] => [activeRect.width - x, y];
    const validPoints = points.filter(hasValue).map(reverseX);

    // Draw
    validPoints.forEach(point => {
      const [x, y] = point;
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x, y, 3, 0, 2 * Math.PI);
      ctx.fill();
      ctx.fillStyle = 'black';
      ctx.fillText(`${x.toFixed(2)}, ${y.toFixed(2)}`, x, y);
      validPoints.forEach(otherPoint => {
        ctx.strokeStyle = color;
        ctx.lineWidth = 0.5;
        ctx.setLineDash([10]);
        const [ox, oy] = otherPoint;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(ox, oy);
        ctx.stroke();
        ctx.setLineDash([]);
      });
    });
  });
};

const onFaceMeshChange = () => {
  const ctx = debugCanvas.value.getContext('2d');
  const results = debugInfo.faceResults;
  const drawingUtils = new DrawingUtils(ctx);
  for (const landmarks of results
    .filter(r => r.mesh)
    .map(r =>
      r.mesh.map(p => ({
        ...p,
        x: 1 - p.x,
      })),
    )) {
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_TESSELATION, {
      color: '#C0C0C070',
      lineWidth: 1,
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_RIGHT_EYE, {
      color: '#FF3030',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_RIGHT_EYEBROW, {
      color: '#FF3030',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_LEFT_EYE, {
      color: '#30FF30',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_LEFT_EYEBROW, {
      color: '#30FF30',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_FACE_OVAL, {
      color: '#E0E0E0',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_LIPS, {
      color: '#E0E0E0',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_RIGHT_IRIS, {
      color: '#FF3030',
    });
    drawingUtils.drawConnectors(landmarks, FaceLandmarker.FACE_LANDMARKS_LEFT_IRIS, {
      color: '#30FF30',
    });
  }
};

const onFaceMaskRectChange = () => {
  const rects = debugInfo.faceMaskRect;
  const activeRect = rects?.active_rect;
  // console.log('[DEBUG] faceMaskRect', rects);
  debugCanvas.value.width = activeRect.width;
  debugCanvas.value.height = activeRect.height;

  if (activeRect) {
    const ctx = debugCanvas.value.getContext('2d');
    drawRect(
      ctx,
      {
        x: activeRect.left,
        y: activeRect.top,
        width: activeRect.width,
        height: activeRect.height,
      },
      '#00a0ff',
      2.0,
    );
  }
};

const onFaceSizeRecordChange = () => {
  const recs = debugInfo.faceSizeRecord;
  const rects = debugInfo.faceMaskRect;
  const activeRect = rects?.active_rect;
  // console.log('[DEBUG] faceSizeRecord', recs);
  const drawRectConditions = (rec: Types.FaceSizeRecordCommon) => {
    if (rec) {
      const getColor = (fail: boolean) => (fail ? '#ffaaaaaa' : '#00ff00aa');
      const rect = rec.middle_rect;
      if (rec.fail_condition.top_fail !== undefined) {
        drawLine({
          x1: activeRect.width - rect.left,
          y1: rect.top,
          x2: activeRect.width - rect.right,
          y2: rect.top,
          color: getColor(rec.fail_condition.top_fail),
        });
      }
      if (rec.fail_condition.left_fail !== undefined) {
        drawLine({
          x1: activeRect.width - rect.left,
          y1: rect.top,
          x2: activeRect.width - rect.left,
          y2: rect.bottom,
          color: getColor(rec.fail_condition.left_fail),
        });
      }
      if (rec.fail_condition.right_fail !== undefined) {
        drawLine({
          x1: activeRect.width - rect.right,
          y1: rect.top,
          x2: activeRect.width - rect.right,
          y2: rect.bottom,
          color: getColor(rec.fail_condition.right_fail),
        });
      }
      if (rec.fail_condition.bottom_fail !== undefined) {
        drawLine({
          x1: activeRect.width - rect.left,
          y1: rect.bottom,
          x2: activeRect.width - rect.right,
          y2: rect.bottom,
          color: getColor(rec.fail_condition.bottom_fail),
        });
      }
    }
  };
  if (recs?.too_big_check) {
    drawRectConditions(recs.too_big_check);
  }
  if (recs?.too_small_check) {
    drawRectConditions(recs.too_small_check);
  }
};

watch(
  [() => debugInfo.faceMaskRect, () => debugInfo.faceResults, () => debugInfo.faceSizeRecord],
  () => {
    const ctx = debugCanvas.value.getContext('2d');
    requestAnimationFrame(() => {
      ctx.clearRect(0, 0, debugCanvas.value.width, debugCanvas.value.height);
      onFaceMaskRectChange();
      onFaceResultsChange();
      onFaceSizeRecordChange();
      onFaceMeshChange();
    });

    if (!currentAction.value) return;

    const { minTargetYaw, maxTargetYaw, minTargetPitch, maxTargetPitch } = buildThreshold(
      currentAction.value,
    );
    minYaw.value = minTargetYaw;
    maxYaw.value = maxTargetYaw;

    minPitch.value = minTargetPitch;
    maxPitch.value = maxTargetPitch;
  },
);

watch(selectedAnimation, newValue => {
  if (newValue) {
    playAnimation(newValue as any);
  }
});

defineExpose({
  debugCanvas,
});
</script>

<template>
  <div class="liveness-debug-overlay">
    <canvas id="face-landmarks" ref="debugCanvas" class="face-landmarks" />
    <div class="debug-top-left has-text-white">
      <div class="has-text-weight-bold">
        <div>{{ currentAction }}</div>
        <div>State: {{ debugInfo.state }}</div>
      </div>

      <div class="info-container">
        <!-- Blendshapes -->
        <template v-if="sortedBlendshapes.length">
          <div class="info-header" @click="showBlendshapes = !showBlendshapes">
            Blendshapes {{ showBlendshapes ? '▼' : '▶' }}
          </div>
          <div v-if="showBlendshapes" class="info-list">
            <label class="checkbox">
              <input v-model="enableBlendshapeSorting" type="checkbox" />
              Enable Sorting
            </label>
            <div v-for="(shape, index) in sortedBlendshapes" :key="index" class="blendshape-item">
              <div class="blendshape-label">{{ shape.name }}</div>
              <div class="blendshape-bar-container">
                <div
                  class="blendshape-bar"
                  :style="{
                    width: `${shape.score * 100}%`,
                    backgroundColor: getBlendshapeColor(shape.score),
                  }"
                ></div>
                <span class="blendshape-value">{{ (shape.score * 100).toFixed(0) }}%</span>
              </div>
            </div>
          </div>
        </template>

        <!-- MoreInfo -->
        <div class="info-header" @click="showingMoreInfo = !showingMoreInfo">
          MoreInfo {{ showingMoreInfo ? '▼' : '▶' }}
        </div>
        <div v-if="showingMoreInfo" class="info-list">
          <strong>faceSize</strong>
          <textarea
            aria-label="faceSizeRecord"
            :value="JSON.stringify(debugInfo.faceSizeRecord, null, 2)"
          ></textarea>
          <strong>initLog</strong>
          <textarea
            aria-label="initLog"
            :value="JSON.stringify(debugInfo.initLog, null, 2)"
          ></textarea>
        </div>

        <!-- Actions -->
        <div class="info-header" @click="showingActions = !showingActions">
          Actions {{ showingActions ? '▼' : '▶' }}
        </div>
        <div v-if="showingActions" class="info-list grid grid-cols-2">
          <div>
            <label class="checkbox">
              <input v-model="freezeTime" type="checkbox" />
              Freeze
            </label>
          </div>
          <div>
            <label class="checkbox">
              <input v-model="dontPass" type="checkbox" />
              Don't Pass
            </label>
          </div>
          <select v-model="selectedAnimation" class="animation-selector">
            <option v-for="anim in animationOptions" :key="anim" :value="anim">
              Play: {{ anim }}
            </option>
          </select>
        </div>
      </div>

      <div>{{ actionProgress?.toFixed(2) }}%</div>

      <!-- Yaw / Pitch -->
      <div class="liveness-angles-display">
        <div>Y: {{ currentYaw?.toFixed(2) }} P:{{ currentPitch?.toFixed(2) }}</div>

        <!-- Yaw -->
        <input
          aria-label="yaw"
          type="range"
          list="yawmarks"
          :value="currentYaw"
          :min="0"
          :max="180"
          disabled
        />

        <datalist id="yawmarks">
          <option :value="minYaw" :label="`${minYaw}`" />
          <option :value="maxYaw" :label="`${maxYaw}`" />
        </datalist>

        <!-- Pitch -->
        <input
          aria-label="pitch"
          type="range"
          list="pitchmarks"
          :value="currentPitch"
          :min="0"
          :max="180"
          disabled
          :style="{ rotate: '-90deg', translate: '80px -20px', width: '80px' }"
        />

        <datalist id="pitchmarks">
          <option :value="minPitch" :label="`${minPitch}`" />
          <option :value="maxPitch" :label="`${maxPitch}`" />
        </datalist>
      </div>

      <!-- Existing face results section -->
      <div v-for="(face, i) in debugInfo.faceResults" :key="`face${i}`">
        <table>
          <th>Face table</th>
          <td>
            <strong>Face {{ i + 1 }}</strong>
          </td>
        </table>
      </div>
      <em v-if="currentHistory?.deltaMs">{{ (1000 / currentHistory?.deltaMs).toFixed(1) }}fps</em>
      <div class="debug-images">
        <div v-for="(record, i) in recordedDataList" :key="`rec${i}`">
          <span class="filename">{{ record.filename }}</span>
          <img :src="record.url" style="height: 50px" alt="debug" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.liveness-debug-overlay {
  input[type='checkbox'] {
    height: unset;
  }
  textarea {
    width: 100%;
    font-family: monospace;
    font-size: xx-small;
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
  .debug-top-left {
    width: 50%;
    font-size: x-small;
    position: absolute;
    // top: 10%;
    z-index: 20000;
    background: rgba(255, 255, 255, 0.1);
  }

  .face-landmarks {
    pointer-events: none;
    z-index: 10000;
    transform: scaleX(1);
    background-position: center center;
    position: absolute;
    overflow: hidden;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-size: 500vh;
  }
  .debug-color-info {
    background: rgba(255, 255, 255, 0.1);
  }
  table {
    table-layout: fixed;
    border-collapse: collapse;
  }
  table,
  th,
  td {
    border: 1px solid black;
  }
  .filename {
    position: absolute;
  }
}

.liveness-angles-display {
  --input-height: 1rem;
}

.animation-selector {
  color: black;
  font-size: x-small;
  height: auto;
  padding: 0;
  margin: 3px;
}

/* Blendshape styles */
.info-container {
  margin-top: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 4px;
  max-width: 250px;
}

.info-header {
  cursor: pointer;
  font-weight: bold;
  font-size: 0.7rem;
}

.info-list {
  max-height: 150px;
  overflow-y: auto;
}

.blendshape-item {
  display: flex;
  flex-direction: column;
  margin: 2px 0;
}

.blendshape-label {
  font-size: 0.6rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.blendshape-bar-container {
  display: flex;
  align-items: center;
  background-color: rgba(50, 50, 50, 0.5);
  height: 4px;
  border-radius: 2px;
  width: 100%;
}

.blendshape-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.2s ease;
}

.blendshape-value {
  font-size: 0.5rem;
  margin-left: 2px;
}
</style>
