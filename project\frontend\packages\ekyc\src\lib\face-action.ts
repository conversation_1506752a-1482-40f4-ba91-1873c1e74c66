/* eslint-disable camelcase */
import merge from 'lodash/merge';

const STATIC_DATA = {
  none: {
    name: 'none',
    category: 'none',
    need_save: false,
  },
  idle: {
    name: 'idle',
    category: 'emotion',
    need_save: true,
  },
  turn_left: {
    name: 'turn_left',
    category: 'movement',
    need_save: false,
  },
  turn_right: {
    name: 'turn_right',
    category: 'movement',
    need_save: false,
  },
  idle_background: {
    name: 'idle_background',
    category: 'movement',
    need_save: true,
  },
} as const;

export class FaceAction {
  name: keyof typeof STATIC_DATA = 'none';

  category: string = 'none';

  need_save: boolean = false;

  // eslint-disable-next-line no-use-before-define
  static DATA = STATIC_DATA;

  constructor(param: string | Partial<FaceAction>) {
    if (typeof param === 'string') {
      merge(this, FaceAction.DATA[param]);
    } else {
      merge(this, param);
    }
  }
}

export default { FaceAction };
