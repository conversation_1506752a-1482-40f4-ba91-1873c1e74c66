<script setup lang="ts">
import { promiseTimeout, templateRef, until, useSessionStorage } from '@vueuse/core';
import cloneDeep from 'lodash/cloneDeep';
import get from 'lodash/get';
import throttle from 'lodash/throttle';

import { getUserAgent } from '@helpers/helpers/user-agent';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useFaceAction } from '@ekyc/composables/liveness/use-face-action';
import { useFramesSequences } from '@ekyc/composables/liveness/use-frames-sequences';
import { useHistory } from '@ekyc/composables/liveness/use-history';
import { useLivenessState } from '@ekyc/composables/liveness/use-liveness-state';
import { useRecordedData } from '@ekyc/composables/liveness/use-recorded-data';
import { useTimer } from '@ekyc/composables/liveness/use-timer';
import { useCamera } from '@ekyc/composables/use-camera';
import { useConfirmCancel } from '@ekyc/composables/use-confirm-cancel';
import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { playAnimation } from '@ekyc/helpers/animations';
import { calculateFaceMaskRect } from '@ekyc/helpers/face-mask-rect';
import { checkFaceSizeFromLandmark } from '@ekyc/helpers/face-size-checker';
import { getFrameBlob } from '@ekyc/helpers/image-utils';
import { detect, loadModel } from '@ekyc/helpers/models';
import type { LivenessLog } from '@ekyc/lib/liveness-log';
import { sendLivenessStart } from '@ekyc/services/ekyc';

import ConfirmCancelModal from './ConfirmCancelModal.vue';
import DebugLivenessOverlay from './DebugLivenessOverlay.vue';
import MovingPixel from './MovingPixel.vue';
import FaceMaskSvgNext from './liveness-modal/FaceMaskSvgNext.vue';
import LivenessModalActionProgression from './liveness-modal/LivenessModalActionProgression.vue';

const workingConstraints = useSessionStorage('uppass-media-liveness-constraints', {});

const props = defineProps({});

const emit = defineEmits<(e: 'finish', payload: any) => void>();

const videoRef = templateRef('videoRef');

const { allSettings, waitTimeBetweenActions } = useEkycSettings();

const { facingMode, isFlipped, initOnResizeWatch, loadCamera, stopPreview } = useCamera({
  videoRef,
  highQuality: false,
  startFacingMode: 'user',
  overrideConstraints: computed(() => allSettings.liveness.additional_constraints),
  workingConstraints,
});

const overrideProcessingImage = computed(() => allSettings.liveness.override_images.processing);

function exitPreview() {
  stopPreview();
}

const { t } = useI18n();
const {
  errorMessages,
  numActionPassed,
  snapFrameNumber,
  actionFrameNumber,
  isActionPassed,
  isActionFailed,
  allLogs,
  livenessTimeStart,
  recordingState,
  isLoading,
  isRecording,
  isShowingInstruction,
  isShowingCloseBtn,
  addErrorMessage,
  resetLivenessState,
  createLivenessLog,
  addLivenessLogs,
  assignLatestLivenessLogResult,
} = useLivenessState();
const { recordedDataList, newRecordedData, lastImageToShow, addRecordedData, resetRecordedData } =
  useRecordedData();
const { detectionHistory, currentHistory, addDetectionHistory, clearDetectionHistories } =
  useHistory();
const {
  actionList,
  currentAction,
  state,
  nextState,
  logs,
  actionProgress,
  enableIdleOnly,
  setToNotPassState,
  checkAction,
  getNextAction,
  resetFaceAction,
  loadActionList,
  ...faceAction
} = useFaceAction();
const { recordTimeLeft, currentRecordTimeMax, startTimer, stopTimer, resetTimer } = useTimer({
  precise: true,
});
const {
  actionFramesSequences,
  missingFramesSequences,
  processActionFrame,
  processMissingFrameByFaceResults,
} = useFramesSequences();
const { debugMode, debugOverlay, dontPass, forcePass, setDebugInfo, setCanvasSize } = useDebug();

function doEmit(payload: object) {
  assignLatestLivenessLogResult({
    action_sequences: actionFramesSequences.value,
    missing_frames_sequences: missingFramesSequences.value,
    time_left: recordTimeLeft.value,
  });
  emit('finish', { ...payload, logs: allLogs.value, isRecording: isRecording.value });
}

async function saveLatestImage(success: boolean) {
  newRecordedData.value = await getFrameBlob(videoRef.value, { maxHeight: 1080 });

  const latestToSave = cloneDeep(newRecordedData.value);
  latestToSave.filename = `${currentAction.value}${success ? '' : '_fail'}_0.jpg`;
  addRecordedData(latestToSave);
}

const { isConfirmCancelModalActive, onClickModalAction } = useConfirmCancel(async () => {
  await saveLatestImage(false);
  doEmit({ action: 'close', data: recordedDataList.value });
  stopTimer();
  exitPreview();
});

const faceMaskRef = ref();

// Computed
const isDoneLastAction = computed(() => numActionPassed.value === actionList.value.length);

// Methods

function doCheckAction(): boolean {
  if (forcePass.value) {
    forcePass.value = false;
    setDebugInfo('status', 'Force pass...');
    return true;
  }

  const { passed, state, logs: checkActionLogs } = checkAction();

  setDebugInfo('actionLogs', checkActionLogs);
  setDebugInfo('status', state);

  return passed;
}

function checkFaceDetection(
  faceResults: Types.DetectFunctionResult[],
  rects: Types.FaceMaskRectInfo,
) {
  let faceSizeRecord: Types.FaceSizeRecord = null;
  /* **** Detection Check **** */

  /** Check if Not started yet (detect for model warmup) */
  const checkIsNotStarted = () => recordingState.value === 'loading';

  const checkIsNoFace = () => {
    if (faceResults.length === 0) {
      addErrorMessage('ekyc.recorder.error_no_face');
      return true;
    }
    return false;
  };

  const checkIsMultipleFace = () => {
    if (faceResults.length > 1) {
      console.warn('[Liveness]', t('ekyc.recorder.error_multiple_faces'));
      addErrorMessage('ekyc.recorder.error_multiple_faces');
      return true;
    }
    return false;
  };

  if (checkIsNotStarted() || checkIsNoFace() || checkIsMultipleFace()) {
    return { passed: false, faceSizeRecord: null };
  }

  /* **** Single Face Check **** */
  if (allSettings.liveness.liveness.enableFaceSize && currentAction.value === 'idle') {
    const { isFaceTooBig, isFaceTooSmall, record } = checkFaceSizeFromLandmark(
      faceResults[0].landmarks,
      rects,
    );
    faceSizeRecord = record;

    if (isFaceTooSmall) {
      console.warn('[Liveness]', t('ekyc.recorder.error_small_face'));
      addErrorMessage('ekyc.recorder.error_small_face');
    }
    if (isFaceTooBig) {
      console.warn('[Liveness]', t('ekyc.recorder.error_big_face'));
      addErrorMessage('ekyc.recorder.error_big_face');
    }

    if (isFaceTooSmall || isFaceTooBig) {
      return { passed: false, faceSizeRecord };
    }
  }
  return { passed: true, faceSizeRecord };
}

function detectAndCheckAction(faceResults: Types.DetectFunctionResult[]) {
  /* **** Face Recognition **** */
  setDebugInfo('status', 'Detecting...');

  const currentDetectStartedAt = Date.now();
  const lastDetectedAt = currentHistory.value?.detectStartedAt;
  const frameDeltaMs = lastDetectedAt ? currentDetectStartedAt - lastDetectedAt : 0;

  const faceResult = faceResults[0];
  const newHistory: Types.FaceDetectionResult = {
    ...faceResult,
    detectionPassed: false,
    detectStartedAt: currentDetectStartedAt,
    deltaMs: frameDeltaMs,
  };
  addDetectionHistory(newHistory);

  logs.value = [];

  errorMessages.value = [];

  const rects = calculateFaceMaskRect(videoRef.value, faceMaskRef.value);

  /* **** Face Detection Check (Count, Size) **** */
  const { passed: faceDetectionPassed, faceSizeRecord } = checkFaceDetection(faceResults, rects);

  currentHistory.value.detectionPassed = faceDetectionPassed;

  setDebugInfo('faceMaskRect', rects);
  setDebugInfo('faceResults', faceResults);
  setDebugInfo('faceSizeRecord', faceSizeRecord);

  if (!faceDetectionPassed) {
    actionProgress.value = 0;

    return {
      detections: [...detectionHistory.value] as Types.FaceDetectionResult[],
      action: currentAction.value,
      passed: false,
      finished: false,
      frame_delta_ms: frameDeltaMs,
      state: state.value,
      next_state: nextState.value,
      checker_logs: logs.value,
      face_mask_rect: rects,
      face_size_record: faceSizeRecord,
    };
  }

  /* **** Liveness Check **** */
  const passed = doCheckAction();

  if (!passed && actionProgress.value <= 0 && enableIdleOnly.value) {
    console.warn('[Liveness]', t('ekyc.recorder.error_not_idle'));
    addErrorMessage(`ekyc.recorder.error_not_idle`);
  }

  const finished = passed;

  const record: Types.FaceDetectionRecord = {
    detections: [...detectionHistory.value] as Types.FaceDetectionResult[],
    action: currentAction.value,
    passed,
    finished,
    frame_delta_ms: frameDeltaMs,
    state: state.value,
    next_state: nextState.value,
    checker_logs: logs.value,
    face_mask_rect: rects,
    face_size_record: faceSizeRecord,
    action_progress: actionProgress.value,
  };

  return record;
}

function doVibrate(pattern: number | number[] = 200) {
  window.navigator.vibrate?.(pattern);
}

const logSnap = throttle((livenessLog: LivenessLog) => {
  console.group(
    `[Liveness] %cSnap ${snapFrameNumber.value} (${currentAction.value}:${actionFrameNumber.value})`,
    'font-size: 20px;',
  );
  console.log(
    `[Liveness] Passed/Finished: %c${livenessLog.result.passed} %c${livenessLog.result.finished}`,
    `background-color: ${livenessLog.result.passed ? 'green' : 'red'};`,
    `background-color: ${livenessLog.result.finished ? 'green' : 'red'};`,
  );
  console.log(`Delta: ${currentHistory.value?.deltaMs}`, `LivenessLog:`, livenessLog);
  console.table(livenessLog.result.checker_logs);
  console.groupEnd();
}, 1000);

/* Liveness snap loop */
async function snapFunction(
  results: Types.DetectFunctionResult[],
): Promise<'complete' | 'continue' | 'timeout' | 'error'> {
  try {
    // CHECK timeout
    if (recordTimeLeft.value <= 0) {
      console.log('[Liveness] Timeout...');
      isActionPassed.value = false;
      isActionFailed.value = true;
      return 'timeout';
    }

    // CHECK timer < 90%, start telling errors
    const isTimerBelow90 = recordTimeLeft.value < currentRecordTimeMax.value * 0.9;
    if (isTimerBelow90 && recordingState.value !== 'background-checking') {
      recordingState.value = 'recording';
    }

    // INIT loop info
    snapFrameNumber.value += 1;
    actionFrameNumber.value += 1;
    const livenessLog = createLivenessLog();
    addLivenessLogs(livenessLog);
    processActionFrame(currentAction.value);

    // DETECT
    const detectionRecord = detectAndCheckAction(results);
    livenessLog.setInfoByFaceDetectionRecord(detectionRecord);
    processMissingFrameByFaceResults(results);

    // DEBUG
    logSnap(livenessLog);
    setDebugInfo('state', detectionRecord.state);

    /** ======================= STEP 1: CHECKING STATE ======================= */
    // DEBUG: Don't pass
    if (dontPass.value) {
      detectionRecord.passed = false;
      detectionRecord.finished = false;
      detectionRecord.state = 'checking';
      detectionRecord.next_state = 'checking';
      actionProgress.value = 0;
      setToNotPassState();
    }

    /** ======================= STEP 2: FINISHED ======================= */
    if (actionProgress.value >= 100) {
      console.log('[Liveness] PASSED:', currentAction.value);

      // SNAP
      await saveLatestImage(true);
      livenessLog.setInfoByRecordedData(newRecordedData.value);

      // Wait for progress bar to fill
      await promiseTimeout(300);
      actionProgress.value = 0;
      numActionPassed.value += 1;
      actionFrameNumber.value = 0;
      doVibrate();
    } else {
      return 'continue';
    }

    /** ======================= STEP 3: NEXT ACTION ======================= */
    if (!isDoneLastAction.value) {
      getNextAction();
      resetTimer();

      if (currentAction.value === 'idle_background') {
        recordingState.value = 'background-checking';
      }

      return 'continue';
    }

    /** ======================= STEP 4: ALL ACTIONS PASSED ======================= */
    isActionPassed.value = true;

    return 'complete';
  } catch (err) {
    console.error('snapFunction error:', err);
    return 'error';
  }
}

async function onDetected(results: Types.DetectFunctionResult[]): Promise<boolean> {
  const snapOp = await snapFunction(results);

  // Snap finish Log
  const livenessTimeSpent = new Date().valueOf() - livenessTimeStart.value.valueOf();
  assignLatestLivenessLogResult({
    liveness_time_spent: livenessTimeSpent,
    time_left: recordTimeLeft.value,
  });

  const errCode =
    recordingState.value === 'background-checking'
      ? 'error_background_checking_time_out'
      : 'error_action_time_out';

  // Snap next operation
  switch (snapOp) {
    case 'complete':
      console.log(`[Liveness] %cLiveness Time spent ${livenessTimeSpent}`, 'font-size: 20px;');
      doEmit({
        action: 'complete',
        data: recordedDataList.value,
        facingMode: facingMode.value,
        flipped: isFlipped.value,
      });
      stopTimer();
      await promiseTimeout((waitTimeBetweenActions.value + 0.5) * 1000);
      exitPreview();
      recordingState.value = 'uploading';
      return true;
    case 'timeout':
      assignLatestLivenessLogResult({
        passed: false,
        error_message: t(`ekyc.recorder.${errCode}`) as string,
      });

      await saveLatestImage(false);
      doEmit({
        action: 'fail',
        data: recordedDataList.value,
        code: errCode,
        error: `ekyc.recorder.${errCode}`,
      });
      stopTimer();
      await promiseTimeout((waitTimeBetweenActions.value + 0.5) * 1000);
      exitPreview();
      recordingState.value = 'uploading';
      return false;
    case 'error':
      doEmit({
        action: 'error',
        data: recordedDataList.value,
        code: 'error_not_supported',
        error: 'ekyc.recorder.error_not_supported',
      });
      stopTimer();
      exitPreview();
      return false;
    default:
      requestAnimationFrame(nextSnap);
      return false;
  }
}

async function nextSnap() {
  const results = await detect(videoRef.value);
  if (results) {
    onDetected(results);
  } else {
    requestAnimationFrame(nextSnap);
  }
}

async function startLiveness() {
  resetLivenessState();

  livenessTimeStart.value = new Date();
  sendLivenessStart({ logs: allLogs.value }).catch(() => {});
  recordingState.value = 'early-recording';
  snapFrameNumber.value = 0;
  actionFrameNumber.value = 0;
  getNextAction();
  resetTimer();
  startTimer();
  nextSnap();

  playAnimation('startChecking');
}

async function initFunction() {
  const initLog = {
    version: '2025-06-11',
    init_time_spent: -1,
    load_model: {},
    load_camera: {},
    ua: getUserAgent(),
    performance: {},
  };
  const logStopTime = () => {
    const initStopTime = new Date();
    const initTimeSpent = initStopTime.valueOf() - initStartTime.valueOf();
    initLog.init_time_spent = initTimeSpent;
  };
  const initStartTime = new Date();
  try {
    console.log('[Liveness] %cInitializing', 'font-size: 20px;');
    initOnResizeWatch();
    resetRecordedData();
    resetLivenessState();
    resetFaceAction();
    clearDetectionHistories();

    faceMaskRef.value.draw();

    setDebugInfo('status', 'Initializing...');

    const lc = await loadCamera();
    await until(() => videoRef.value?.readyState).toBe(4, { timeout: 10000, throwOnTimeout: true });
    const lm = await loadModel();

    setDebugInfo('video', videoRef.value);

    initLog.load_model = lm;
    initLog.load_camera = lc;

    setDebugInfo('initLog', initLog);

    try {
      initLog.performance = { ...JSON.parse(JSON.stringify(window.performance)) };
    } catch (error) {
      console.warn('Can not get performance log.', error);
    }

    if (!lc.success) {
      throw new Error('error_open_camera');
    }

    try {
      initLog.performance = { ...JSON.parse(JSON.stringify(window.performance)) };
    } catch (error) {
      console.warn('Can not get performance log.', error);
    }

    setDebugInfo('initLog', initLog);
    setDebugInfo('status', 'Getting action list...');
    setCanvasSize(videoRef.value.videoWidth, videoRef.value.videoHeight);

    logStopTime();

    await loadActionList({ log: initLog }).catch(err => {
      console.error('loadActionList error:', err);
      if (get(err, 'response.data.error_type') === 'max_attempt') {
        throw new Error('error_liveness_max_attempt');
      } else {
        throw new Error('error_liveness_get_action');
      }
    });
    setDebugInfo('status', 'Ready to start!');

    recordingState.value = 'pre-recording';
    recordedDataList.value = [];
  } catch (err) {
    console.error(err);
    logStopTime();

    const VALID_ERR = [
      'error_open_camera',
      'error_liveness_max_attempt',
      'error_liveness_get_action',
    ];
    const errType = VALID_ERR.includes(err.message) ? err.message : 'error_liveness_init_function';
    const errText = `ekyc.recorder.${errType}`;
    const errObj = {
      action: 'error',
      code: errType,
      status: errType,
      error: errText,
      error_type: errType,
      message: err.message,
      detail: err?.toString(),
      init_log: initLog,
    };
    // Delay modal close
    setTimeout(() => {
      doEmit(errObj);
      stopTimer();
      exitPreview();
    }, 1000);
  }
}

onMounted(() => {
  initFunction();
});

onBeforeUnmount(() => {
  stopTimer();
  exitPreview();
});

defineExpose({
  ...faceAction,
});
</script>

<template>
  <div>
    <div class="modal is-active modal-full-screen mask-modal">
      <div class="modal-background" />
      <div class="modal-content modal-card camera-view mode-liveness">
        <DebugLivenessOverlay v-if="debugMode" ref="debugOverlay" />
        <a
          v-if="isShowingCloseBtn"
          class="mdi mdi-close close-btn"
          aria-label="close"
          @click="isConfirmCancelModalActive = true"
        />

        <section class="modal-card-body">
          <div class="canvas-wrapper">
            <!-- Instruction -->
            <div v-if="isShowingInstruction" class="instruction-overlay">
              <div class="action-button-wrapper">
                <button
                  class="button is-primary is-large is-fullwidth"
                  :class="{ 'is-loading': isLoading }"
                  type="button"
                  @click.prevent="!isLoading && startLiveness()"
                >
                  {{ t('ekyc.video.start') }}
                </button>
              </div>
            </div>

            <!-- Loader Overlay -->
            <template v-if="recordingState === 'uploading' && lastImageToShow">
              <!-- Freeze Last image preview -->
              <div class="image-capture-preview-wrapper">
                <img
                  :src="lastImageToShow"
                  class="image-capture-preview-item flipped"
                  alt="preview"
                />
              </div>
            </template>

            <!-- Pass Overlay -->
            <template v-if="isActionPassed">
              <!-- Uploading message -->
              <template v-if="recordingState === 'uploading'">
                <div class="liveness-action-overlay">
                  <div class="ekyc-action-overlay ekyc-action-result uploading is-animated">
                    <div class="processing-image-wrapper image">
                      <img
                        v-if="overrideProcessingImage"
                        :src="overrideProcessingImage"
                        class="processing-image"
                        alt="processing"
                      />
                      <div v-else class="loader" style="font-size: 50px" />
                    </div>

                    <div class="title is-spaced">
                      {{ t('ekyc.recorder.verifying_liveness') }}
                    </div>
                    <div class="subtitle">
                      {{ t('ekyc.recorder.please_wait') }}
                    </div>
                  </div>
                </div>
              </template>
              <!-- Recording message -->
              <template v-else>
                <div class="liveness-action-overlay">
                  <div class="ekyc-action-overlay ekyc-action-result passed is-animated">
                    <span class="icon is-large">
                      <span class="mdi mdi-check-circle-outline" />
                    </span>
                    <span v-if="isDoneLastAction" class="title">
                      {{ t('ekyc.recorder.scan_complete') }}
                    </span>
                    <span v-else class="title">
                      {{ t('ekyc.recorder.action_passed') }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
            <!-- Fail Overlay -->
            <template v-else-if="isActionFailed">
              <!-- Uploading & Recording message -->
              <div class="liveness-action-overlay">
                <div class="ekyc-action-overlay ekyc-action-result failed is-animated">
                  <span class="icon is-large">
                    <span class="mdi mdi-close-circle-outline" />
                  </span>
                  <span class="title">
                    {{ t('ekyc.recorder.action_failed') }}
                  </span>
                </div>
              </div>
            </template>

            <!-- Overlay  -->
            <template v-if="!(isActionPassed || isActionFailed)">
              <!-- Action Progression -->
              <LivenessModalActionProgression :is-flipped="isFlipped" />
              <!-- Background Checking Overlay -->
              <template v-if="recordingState === 'background-checking'">
                <div class="liveness-action-overlay">
                  <div class="ekyc-action-overlay ekyc-action-result background-checking">
                    <span class="icon is-large">
                      <span class="mdi mdi-clock-outline" />
                    </span>
                    <span class="title">
                      {{ t('ekyc.recorder.action_wait_background_checking') }}
                    </span>
                  </div>
                </div>
              </template>
            </template>

            <!-- Camera -->
            <video
              id="camera"
              ref="videoRef"
              muted
              playsinline
              preload="auto"
              poster="https://cdn.uppass.io/images/transparent.png"
            />
            <FaceMaskSvgNext ref="faceMaskRef" id="face-mask-svg" />
            <MovingPixel />
          </div>
        </section>
      </div>
    </div>
    <ConfirmCancelModal
      :question="t('ekyc.video.cancel').toString()"
      :is-active="isConfirmCancelModalActive"
      @yes="() => onClickModalAction(true)"
      @no="() => onClickModalAction(false)"
    />
  </div>
</template>

<style lang="scss" scoped>
$progress-indeterminate-duration: 1s;
$progress-border-radius: 0px;
.modal.mask-modal .modal-card.camera-view.mode-liveness {
  .canvas-wrapper {
    height: 100%;
    width: 100%;
    background: black;
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    justify-content: center;
    align-content: center;
  }

  .ekyc-action-result {
    .title {
      margin-top: 1.5rem;
    }
  }

  #camera,
  :deep(.liveness-debug-overlay .face-landmarks) {
    position: fixed;

    object-fit: cover;
    max-height: unset;
    max-width: unset;
  }
}

.close-btn {
  top: 1%;
  right: 2%;
  z-index: 4;
  font-size: 2rem;
  display: block;
  position: absolute;
  color: #fff;
}

.ekyc-action-result.is-animated {
  .icon,
  .image {
    animation: come-to-middle 1s;
    animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }
  .title,
  .subtitle {
    animation: fade-in 1s;
    animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }
}

#face-mask-svg {
  z-index: 1 !important;
}

@keyframes come-to-middle {
  0% {
    transform: translateY(-50vh) scale(1);
  }
  50% {
    transform: translateY(0px) scale(1.5);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  50% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
