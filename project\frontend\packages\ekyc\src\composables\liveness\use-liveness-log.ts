import throttle from 'lodash/throttle';

import { getAveragePixelColor3x3 } from '@ekyc/helpers/image-utils';

import { useFaceAction } from './use-face-action';
import { useLivenessState } from './use-liveness-state';

// Types
export interface LivenessLog {
  action: string[];
  frame_number: number;
  recorded_at: Date;
  result: {
    checker_logs?: any[][];
    action_progress: number;
    action_progress_ms: number;
    image_width: number;
    image_height: number;
    liveness_time_spent: number;
    frame_delta_ms: number;
    log_delta_ms: number;
    time_left: number;
    passed: boolean;
    state: string;
    face_mask_rect: {
      active_rect: Record<string, any>;
      overlay_rect: Record<string, any>;
      face_mask_rect: Record<string, any>;
    };
    face_size_record: Types.FaceSizeRecord | null;
    landmarks: [number, number][][];
    detections: Types.FaceDetectionResult[];
    log_version: string;
    performance: Record<string, any> | null;
    average_color?: string[];
    error_message?: string | null;
    action_sequences?: Types.ActionSequence[];
    missing_frames_sequences?: Types.MissingFramesSequence[];
    can_manual_snap?: boolean;
  };
}

// Helper functions
const getPerformanceLog = () => {
  try {
    return {
      memory: JSON.parse(
        JSON.stringify((window.performance as any)?.memory, [
          'totalJSHeapSize',
          'usedJSHeapSize',
          'jsHeapSizeLimit',
        ]),
      ),
    };
  } catch (error) {
    console.warn('Cannot get performance log.', error);
    return null;
  }
};

const createLivenessLog = (action: any, frameNumber: number): LivenessLog => {
  return {
    action,
    frame_number: frameNumber,
    recorded_at: new Date(),
    result: {
      checker_logs: undefined,
      action_progress: 0,
      action_progress_ms: 0,
      image_width: 0,
      image_height: 0,
      liveness_time_spent: 0,
      frame_delta_ms: 0,
      log_delta_ms: 0,
      time_left: 0,
      passed: false,
      state: '',
      face_mask_rect: {
        active_rect: {},
        overlay_rect: {},
        face_mask_rect: {},
      },
      face_size_record: null,
      landmarks: [],
      detections: [],
      log_version: '2025-01-10',
      // Every 1, 51, 101, ... frame
      performance: frameNumber % 50 === 1 ? getPerformanceLog() : null,
    },
  };
};

// Global State
const allLogs = ref<LivenessLog[]>([]);

export const useLivenessLog = () => {
  let lastTime: number | null = null;
  // const MAX_LOGS = 300; // 10 f/s * 10s * 3 actions
  // const MAX_LOGS = 200; // Reduced because DATA_UPLOAD_MAX_MEMORY_SIZE = 5MB
  const MAX_LOGS = 50; // Reduced because DATA_UPLOAD_MAX_MEMORY_SIZE = 5MB & if use face_mesh log will be exceed 5MB

  // Methods
  const resetAllLogs = () => {
    allLogs.value = [];
  };

  const createLivenessLogWithCurrentState = () => {
    const { currentAction } = useFaceAction();
    const { snapFrameNumber } = useLivenessState();
    return createLivenessLog(currentAction.value, snapFrameNumber.value);
  };

  const addLog = (log: LivenessLog) => {
    const currentTime = Date.now();

    // Assign log delta
    log.result.log_delta_ms = currentTime - (lastTime ?? currentTime);
    lastTime = currentTime;

    let finalLogs = [...allLogs.value, log];
    if (finalLogs.length > MAX_LOGS) {
      const HALF_LENGTH = MAX_LOGS / 2;
      finalLogs = [...finalLogs.slice(0, HALF_LENGTH), ...finalLogs.slice(-HALF_LENGTH)];
    }
    allLogs.value = finalLogs;
  };

  // Log manipulation functions
  const updateLatestLog = (updates: Partial<LivenessLog['result']>) => {
    if (!allLogs.value.length) {
      addLog(createLivenessLogWithCurrentState());
    }
    const latestLog = allLogs.value[allLogs.value.length - 1];
    Object.assign(latestLog.result, updates);
  };

  const updateLogWithRecordedData = (log: LivenessLog, recordedData: Types.RecordedData) => {
    log.result.image_width = recordedData.canvas.width;
    log.result.image_height = recordedData.canvas.height;
    return log;
  };

  const updateLogWithCameraSample = (log: LivenessLog, canvas: HTMLCanvasElement) => {
    log.result.average_color = getAveragePixelColor3x3(canvas);
    return log;
  };

  const updateLogWithFaceDetection = (
    log: LivenessLog,
    detectionRecord: Types.FaceDetectionRecord,
  ) => {
    const latestDetections = detectionRecord.detections?.slice(-1) || [];

    log.result = {
      ...log.result,
      detections: latestDetections.map(d => ({
        ...d,
        recordedData: undefined,
        landmarks: undefined,
      })),
      landmarks: latestDetections.map(
        d => d.landmarks?.positions?.filter(p => p.x !== 0 || p.y !== 0).map(p => [p.x, p.y]) || [],
      ),
      passed: detectionRecord.passed,
      face_mask_rect: detectionRecord.face_mask_rect,
      face_size_record: detectionRecord.face_size_record,
      frame_delta_ms: detectionRecord.frame_delta_ms,
      state: detectionRecord.state,
      checker_logs: detectionRecord.checker_logs,
      action_progress: detectionRecord.action_progress,
      action_progress_ms: detectionRecord.action_progress_ms,
      missing_frames_sequences: detectionRecord.missing_frames_sequences,
    };

    return log;
  };

  return {
    allLogs,
    addLog: throttle(addLog, 100),
    resetAllLogs,
    createLivenessLog,
    createLivenessLogWithCurrentState,
    updateLatestLog,
    updateLogWithRecordedData,
    updateLogWithCameraSample,
    updateLogWithFaceDetection,
  };
};

export default useLivenessLog;
