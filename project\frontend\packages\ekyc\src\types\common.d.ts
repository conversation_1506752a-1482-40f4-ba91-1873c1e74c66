/* eslint-disable camelcase */
declare namespace Types {
  type FaceExpressions =
    import('../helpers/face-api-proxies/classes/FaceExpressions').FaceExpressions;
  type AxiosStatic = import('axios').AxiosStatic;
  type VueI18n = import('vue-i18n').default;
  type Coords3D = import('@mediapipe/tasks-vision').NormalizedLandmark[];
  type FaceLandmarks68 =
    import('../helpers/face-api-proxies/classes/FaceLandmarks68').FaceLandmarks68;
  type FaceAction = import('../lib/face-action').FaceAction;

  interface PluginOptions {
    i18n?: VueI18n;
    components?: object | boolean;
    axios?: AxiosStatic;
    [key: string]: any;
  }

  interface ColorInfo {
    r: number;
    g: number;
    b: number;
    brightness: number;
  }

  interface RecordedData {
    blob: Blob;
    url: string;
    canvas?: HTMLCanvasElement;
    filename?: string;
  }

  type DetectFunctionResult = {
    landmarks?: FaceLandmarks68;
    expressions?: FaceExpressions;
    mesh?: Coords3D;
    blendshape?: Record<Types.PossibleBlendshapeName, number>;
    matrix?: import('@mediapipe/tasks-vision').Matrix;
    probability?: number[];
    input_width?: number;
    input_height?: number;
    resized_width?: number;
    resized_height?: number;
  };

  type PossibleBlendshapeName =
    | '_neutral'
    | 'browDownLeft'
    | 'browDownRight'
    | 'browInnerUp'
    | 'browOuterUpLeft'
    | 'browOuterUpRight'
    | 'cheekPuff'
    | 'cheekSquintLeft'
    | 'cheekSquintRight'
    | 'eyeBlinkLeft'
    | 'eyeBlinkRight'
    | 'eyeLookDownLeft'
    | 'eyeLookDownRight'
    | 'eyeLookInLeft'
    | 'eyeLookInRight'
    | 'eyeLookOutLeft'
    | 'eyeLookOutRight'
    | 'eyeLookUpLeft'
    | 'eyeLookUpRight'
    | 'eyeSquintLeft'
    | 'eyeSquintRight'
    | 'eyeWideLeft'
    | 'eyeWideRight'
    | 'jawForward'
    | 'jawLeft'
    | 'jawOpen'
    | 'jawRight'
    | 'mouthClose'
    | 'mouthDimpleLeft'
    | 'mouthDimpleRight'
    | 'mouthFrownLeft'
    | 'mouthFrownRight'
    | 'mouthFunnel'
    | 'mouthLeft'
    | 'mouthLowerDownLeft'
    | 'mouthLowerDownRight'
    | 'mouthPressLeft'
    | 'mouthPressRight'
    | 'mouthPucker'
    | 'mouthRight'
    | 'mouthRollLower'
    | 'mouthRollUpper'
    | 'mouthShrugLower'
    | 'mouthShrugUpper'
    | 'mouthSmileLeft'
    | 'mouthSmileRight'
    | 'mouthStretchLeft'
    | 'mouthStretchRight'
    | 'mouthUpperUpLeft'
    | 'mouthUpperUpRight'
    | 'noseSneerLeft'
    | 'noseSneerRight';

  type MeshDetectFunctionResult = {
    landmarks: FaceLandmarks68;
    expressions?: FaceExpressions;
    mesh?: Coords3D;
    blendshape?: Record<PossibleBlendshapeName, number>;
    probability?: number[];
    input_width?: number;
    input_height?: number;
    resized_width?: number;
    resized_height?: number;
  };

  interface FaceDetectionResult {
    detectionPassed: boolean;
    landmarks?: FaceLandmarks68;
    mesh?: Coords3D;
    blendshape?: Record<PossibleBlendshapeName, number>;
    matrix?: import('@mediapipe/tasks-vision').Matrix;
    probability?: number[];
    input_width?: number;
    input_height?: number;
    resized_width?: number;
    resized_height?: number;
    detectStartedAt?: number;
    detectEndedAt?: number;
    deltaMs?: number;
  }

  type FaceDetectionRecordState = 'checking' | 'transitioning' | 'ending' | 'finished';

  interface ActionSequence {
    action: string;
    min_frame: number;
    max_frame: number;
  }
  type MissingFramesSequence = [number, number];
  interface CommonRect {
    left: number;
    right: number;
    top: number;
    bottom: number;
    width: number;
    height: number;
    scale?: number;
    computed_width?: number;
    computed_height?: number;
  }
  interface FaceSizeRecordCommon {
    fail_condition: Record<string, boolean>;
    face_rect: {
      left: number;
      right: number;
      bottom: number;
      top: number;
      width: number;
      height: number;
    };
    middle_rect: {
      middle_x_scaling: number;
      middle_y_scaling: number;
      left: number;
      right: number;
      bottom: number;
      top: number;
    };
  }

  interface MethodLog {
    passed: boolean;
    progress: number;
    action: string;
    method: string;
    threshold: any;

    yaw?: number;
    pitch?: number;
    delta?: number;
    passed_frames?: number;
    sum_delta?: number;
    progress_raw?: number;
  }

  interface FaceSizeRecordTooSmall extends FaceSizeRecordCommon {
    fail: boolean;
    fail_condition: {
      left_fail: boolean;
      right_fail: boolean;
      bottom_fail?: boolean;
      top_fail?: boolean;
      face_width_fail: boolean;
    };
  }
  interface FaceSizeRecordTooBig extends FaceSizeRecordCommon {
    fail: boolean;
    fail_condition: {
      left_fail: boolean;
      right_fail: boolean;
      bottom_fail: boolean;
      top_fail: boolean;
      face_width_fail: boolean;
    };
  }
  interface FaceMaskRectInfo {
    camera: { width: number; height: number };
    active_rect: Types.CommonRect;
    overlay_rect: Types.CommonRect;
    face_mask_rect: Types.CommonRect;
  }

  interface FaceSizeRecord {
    fail: boolean;
    too_small_check: FaceSizeRecordTooSmall;
    too_big_check: FaceSizeRecordTooBig;
    relative_face_points?: number[][];
    relative_face_rect?: Types.CommonRect;
  }
  interface FaceDetectionRecord {
    action: string | string[];
    detections: FaceDetectionResult[];
    passed: boolean;
    finished?: boolean;
    face_mask_rect: FaceMaskRectInfo;
    face_size_record: FaceSizeRecord;

    frame_delta_ms?: number;
    state?: FaceDetectionRecordState;
    next_state?: FaceDetectionRecordState;
    checker_logs?: any[][];
    action_progress?: number;
    action_progress_ms?: number;
    missing_frames_sequences?: FaceDetectionRecordMissingFrameSequence[];
  }

  interface CameraBlockRule {
    default: boolean;
    label: boolean;
  }
}
