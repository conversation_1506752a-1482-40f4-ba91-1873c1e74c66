/* eslint-disable camelcase, no-use-before-define */
declare namespace Types {
  type LivenessActionIconSet = 'smiley' | 'cartoon' | 'custom';

  type BaseEkycComponentSetting = {
    ref: string;
    upload_url: string;
    auto_url: string;
    log_url: string;
    result_tracking_url: string;
    check_quality_url: string;
    ensure_facecompare: boolean;
    expose_upload_interface: boolean;
    dummy: boolean;
    retryMax: number;
    retryDelay: number;
    show_privacy_policy: boolean;
    show_popup_desktop: boolean;
    show_popup_webview: boolean;
    show_popup_camera_permission: boolean;
    show_popup_go_to_setting: boolean;
    override_popup_go_to_setting: {
      content: string;
    };
    show_flip_button: boolean;
    watermark_text: string;
    additional_constraints: { video?: MediaTrackConstraints; audio?: MediaTrackConstraints };
    override_messages: any;
    override_images: Record<EkycLivenessImageTypes | EkycDocumentImageTypes, string>;
    configs?: Record<string, any>;
    max_memory_size?: number;
  };

  type LivenessComponentSetting = BaseEkycComponentSetting & {
    face_actions_url: string;
    log_url: string;
    liveness_log_url: string;
    start_url: string;
    cancel_url: string;
    fail_url: string;
    prepare_top_content: string;
    self_timer?: number;
    liveness: {
      backendType:
        | import('@mediapipe/tasks-vision').FaceDetectorOptions['baseOptions']['delegate']
        | 'tf_wasm'
        | 'tf_cpu';
      recordTimeMax: {
        [K in FaceActionPossibleValue | ActionSequencePossibleValue]: number;
      };
      waitTimeBetweenActions: number;
      maxHistorySize: number;
      enableFaceSize: boolean;
      enableIdleOnly: boolean;
      checkers: FaceActionCheckerSetting;
      threshold: {
        [K in FaceActionPossibleValue | ActionSequencePossibleValue]: ThresholdValue;
      };
      actionIconSetName: LivenessActionIconSet;
      action_sequence: ActionSequencePossibleValue[];
      face_size: {
        mask_scale_x: number;
        mask_scale_y: number;
        max_mask_x: number;
        small_scale_x: number;
        small_scale_y: number;
        big_scale_x: number;
        big_scale_y: number;
      };
    };
    override_images: Record<EkycLivenessImageTypes, string>;
  };

  type DocumentComponentSetting = BaseEkycComponentSetting & {
    override_images: Record<EkycDocumentImageTypes, string>;

    include_nfc?: boolean;
    nfc_url?: string;

    include_backcard?: {
      front_card?: boolean;
      driver_license?: boolean;
      residence_permit?: boolean;
      thai_alien_card?: boolean;
    };

    accepted_countries?: Record<EkycDocumentItemTypes, string[]>;

    auto_detect_document_type?: boolean;

    auto_detect_country?: {
      passport?: boolean;
      other_document?: boolean;
    };

    configs?: {
      ignore_face?: boolean;
      ignore_ocr?: boolean;
      check_expiry?: boolean;
      check_id_match_with?: string;
      check_ocr_fields?: {
        field: string;
        document_type?: string;
      }[];
      check_age?: {
        min?: number;
        max?: number;
        field?: 'age' | string;
      };
      check_warning?: boolean | string[];
      enabled_vertical_experience?: boolean;
    };

    frame_size: {
      mask_scale_x: number;
      mask_scale_y: number;
      max_mask_x: number;
    };
  };

  type EkycComponentSetting = LivenessComponentSetting | DocumentComponentSetting;

  type ThresholdValue = {
    face_noise?: number;
    expression?: number;
    cheek_ratio?: number;
    not_turn_left?: number;
    not_turn_right?: number;
    blink_min_score?: number;
    blink_timeout_ms?: number;
    mouth_open_min_score?: number;
    yaw_min?: number;
    yaw_max?: number;
    yaw_range?: number;
    pitch_min?: number;
    pitch_max?: number;
    pitch_range?: number;
    frames?: number;
  };

  type FaceActionPossibleValue = 'idle' | 'turn_left' | 'turn_right' | 'idle_background';

  type ActionSequencePossibleValue = 'blink_twice' | 'mouth_open';

  type PossibleChecker =
    | 'angle'
    | 'pitch_range'
    | 'blink_twice'
    | 'eye_open'
    | 'eye_close'
    | 'mouth_open'
    | 'mouth_close';

  type FaceActionCheckerSetting = {
    [K in FaceActionPossibleValue | ActionSequencePossibleValue]: PossibleChecker[][];
  };

  type EkycMaxAttemptWarningSetting = {
    title?: string;
    description?: string;
    button?: {
      visible?: boolean;
      label?: string;
      target?: string;
      auto_redirect?: boolean;
      actions?: Types.DEAction[];
    };
    max_attempt_count?: number;
    allow_max_attempt_pass?: boolean;
    override_images?: Record<string, string>;
  };

  type EkycResultState =
    | 'hidden'
    | 'preview'
    | 'uploading'
    | 'passed'
    | 'already_passed'
    | 'failed_backend'
    | 'failed_others';

  type EkycMediaTypes = 'liveness' | 'document' | 'backcard';

  type EkycItemTypes = EkycLivenessItemTypes | EkycDocumentItemTypes;

  type EkycLivenessItemTypes = 'liveness';

  type EkycDocumentItemTypes =
    | 'front_card'
    | 'passport'
    | 'driver_license'
    | 'thai_alien_card'
    | 'residence_permit'
    | 'portrait'
    | 'ci_passport'
    | 'work_permit_card'
    | 'work_permit_book'
    | 'travel_document'
    | 'white_card'
    | 'border_pass'
    | 'monk_card'
    | 'immigration_card'
    | 'other_document'
    | 'backcard';

  type EkycCommonImageTypes = 'success' | 'max_attempt' | 'camera_permission' | 'camera_retry';

  type EkycLivenessImageTypes = EkycCommonImageTypes | 'processing' | 'overlay' | 'error_timeout';

  type EkycDocumentImageTypes =
    | EkycCommonImageTypes
    | 'error_both'
    | 'error_face'
    | 'error_ocr'
    | 'error_expiry'
    | 'error_id_match_with'
    | 'error_ocr_fields'
    | 'error_age'
    | 'error_warning';

  type EkycValidationTypes = EkycDocumentValidationTypes | EkycLivenessValidationTypes;

  type EkycDocumentValidationTypes =
    | 'ocr'
    | 'supported_country'
    | 'orientation'
    | 'validity'
    | 'both'
    | 'face'
    | 'mrz'
    | 'mrz_expiry'
    | 'image_quality'
    | 'warning'
    | 'expiry'
    | 'id_match_with'
    | 'ocr_fields'
    | 'age'
    | 'face_compare'
    | 'label'
    | 'missing_frames'
    | 'brightness'
    | 'face_out_frame'
    | 'face_size'
    | 'liveness_detection'
    | 'liveness_detection_face_too_small'
    | 'liveness_detection_face_not_found'
    | 'liveness_detection_too_many_faces'
    | 'liveness_detection_face_is_occluded'
    | 'liveness_detection_face_close_to_border'
    | 'liveness_detection_face_cropped'
    | 'liveness_detection_face_angle_too_large'
    | 'liveness_detection_failed_to_predict_landmarks'
    | 'liveness_detection_service_unavailable'
    | 'liveness_detection_face_too_close'
    | string;

  type EkycLivenessValidationTypes =
    | 'face_compare'
    | 'label'
    | 'missing_frames'
    | 'brightness'
    | 'face_out_frame'
    | 'face_size'
    | 'liveness_detection'
    | 'liveness_detection_face_too_small'
    | 'liveness_detection_face_not_found'
    | 'liveness_detection_too_many_faces'
    | 'liveness_detection_face_is_occluded'
    | 'liveness_detection_face_close_to_border'
    | 'liveness_detection_face_cropped'
    | 'liveness_detection_face_angle_too_large'
    | 'liveness_detection_failed_to_predict_landmarks'
    | 'liveness_detection_service_unavailable'
    | 'liveness_detection_face_too_close'
    | string;

  type EkycUploadApiResult = {
    result_url: string;
    code: number;
    data: {
      result: Record<string, { status: boolean; message?: string }>;
      image_url: { img: string; face_image?: string; idle_0?: string };
    };
    attempt_count?: number;
    attempt_left?: number;
    max_attempt?: boolean;
  };

  type EkycLivenessApiResult = EkycUploadApiResult & {
    data: string;
    result: Record<
      EkycLivenessValidationTypes,
      { status: boolean; message?: string; error_code?: string }
    >;
    status: string;
  };

  type EkycDocumentApiResult = EkycUploadApiResult & {
    ocr: Record<string, string>;
    data: {
      result: Record<EkycDocumentValidationTypes, { status: boolean; message?: string }>;
      status: 'ok';
      image_url: {
        img: string;
        face_image: string;
      };
      request_id: string;
    };
    autofill: {
      failed_fields: string[];
      success_fields: string[];
    };
    result_log: any;
    document_type: EkycDocumentItemTypes;
    gateway_result: {
      image_url: {
        img: string;
        face_image: string;
      };
      request_id: string;
    };
    error_type?: string;
  };

  type EkycRecordBasePayload = {
    logs?: any[];
    flipped?: boolean;
    isRecording?: boolean;
    facingMode?: string;
    data?: Types.RecordedData[];
    attempt_count?: number;
    attempt_left?: number;
    max_attempt?: boolean;
  };
  type EkycRecordCompletePayload = EkycRecordBasePayload & {
    action: 'complete';
    nonce?: string;
    submit_log?: { is_auto_snap?: boolean };
  };
  type EkycRecordErrorPayload = EkycRecordBasePayload & {
    action: 'error';
    error: any;
    code?: string;
    error_type?: string;
    status?: string;
    message?: string;
    detail?: string;
    init_log?: any;
    from?: 'server' | 'init';
    reaching_max_attempt?: boolean;
  };
  type EkycRecordFailPayload = EkycRecordBasePayload & {
    action: 'fail';
    code: string;
    error: any;
    from?: string;
    reaching_max_attempt?: boolean;
  };
  type EkycRecordClosePayload = EkycRecordBasePayload & { action: 'close'; unfocus?: boolean };

  type EkycRecordFinishedPayload =
    | EkycRecordCompletePayload
    | EkycRecordErrorPayload
    | EkycRecordFailPayload
    | EkycRecordClosePayload;

  type PreviewType = {
    url: string;
    isLoading: boolean;
    resultState: Types.EkycResultState;
    failKeys: EkycDocumentValidationTypes[];
    errorMessages: string[];
    success: boolean;
    itemType: EkycDocumentItemTypes;
  };
}
