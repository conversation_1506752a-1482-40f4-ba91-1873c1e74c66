<template>
  <div>
    <div
      v-if="previewInterface.previewType === 'form'"
      class="app__dynamic-form"
      :class="{ 'hide-navbar': !navbarConfig.show_navbar }"
    >
      <NavBar />
      <main class="section">
        <DynamicForm
          ref="formRef"
          :instance="dynamicFormInstance"
          :form-slug="'preview'"
          :init="false"
          :destroy="false"
        />
        <div v-if="showBranding" class="container branding-content">
          <img
            class="branding-img"
            src="https://cdn.uppass.io/logos/powered-by-uppass.svg"
            alt="Powered by Uppass"
          />
        </div>
      </main>
    </div>
    <DefaultLayout v-else>
      <div class="content" :class="pageSchema.builder.type || ''">
        <DynamicComponent
          v-for="(element, field) in pageSchema.items"
          :key="field"
          :ref="field"
          :element="element"
        />
        <div v-if="showBranding" class="container branding-content">
          <img
            class="branding-img"
            src="https://cdn.uppass.io/logos/powered-by-uppass.svg"
            alt="Powered by Uppass"
          />
        </div>
      </div>
      <root-style-injector
        :styling="{
          ...formStyling,
          ...pageSchema.styling,
        }"
      />
    </DefaultLayout>
  </div>
</template>

<script setup lang="ts">
import { type WatchDebouncedOptions, watchDebounced } from '@vueuse/core';

import DynamicForm from '@core/components/DynamicForm/DynamicForm.vue';
import DynamicComponent from '@core/components/dynamic-form/DynamicComponent.vue';
import RootStyleInjector from '@core/components/dynamic-form/RootStyleInjector.vue';
import {
  initAllowFormulaWithFormSetting,
  initInputDataWithFormSetting,
  overrideDocumentWithFormSetting,
  useSchemaConfigFormSettings,
} from '@core/composables/dynamic-form-settings/use-schema-config-form-settings';
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';

import { isInIframe } from '@helpers/helpers/iframe';

import NavBar from '@/components/navbar/NavBar.vue';
import { useNavbarControl } from '@/composables/use-navbar-control';
import { useRouter } from '@/composables/use-router';
import DefaultLayout from '@/layouts/DefaultLayout.vue';

const { navbarConfig } = useNavbarControl();
const { createFormInstance } = useDynamicFormApp();
const { changeLanguage } = useRouter();
const formRef = ref();
const dynamicFormInstance = createFormInstance('preview');
dynamicFormInstance.inBuilder = true;
const { schemaConfigSetting } = useSchemaConfigFormSettings(
  computed(() => dynamicFormInstance.formSlug),
);

const formStyling = computed(() => schemaConfigSetting.value?.styling);
const showBranding = computed(() => schemaConfigSetting.value?.configs?.branding?.show_branding);

const previewInterface = ref<Types.PreviewFrameInterface>({
  previewType: 'form',
  formInstance: dynamicFormInstance,
  schemaConfig: schemaConfigSetting,
  changeLanguage,
});

const pageSchema = computed(
  () => dynamicFormInstance.formSchema?.steps?.page?.sections?.page as Types.ISchemaPage,
);

provide('dynamicFormInstance', dynamicFormInstance);
provide('id', dynamicFormInstance.id);

function testValidation() {
  dynamicFormInstance.formData.validateBeforeSave({
    submit: dynamicFormInstance.formControl.isSubmit,
  });
}

watch(
  () => previewInterface.value.schemaConfig,
  () => {
    overrideDocumentWithFormSetting();
    initAllowFormulaWithFormSetting();
    initInputDataWithFormSetting(dynamicFormInstance);
  },
);
watchDebounced(
  () => dynamicFormInstance.formSchema?.events,
  () => {
    dynamicFormInstance.refreshListeningEvents();
  },
  { deep: true, immediate: true, debounce: 500 } as WatchDebouncedOptions<any>,
);

onMounted(() => {
  if (isInIframe()) {
    /* eslint-disable no-restricted-globals */
    (parent as any).previewInterface = previewInterface;
    const message: Types.PreviewFrameMessageData = {
      from: 'previewer',
      type: 'loaded',
    };
    parent.postMessage(message, window.origin);
  }
});
</script>

<style lang="scss">
body#routePreviewFlow {
  &::-webkit-scrollbar {
    width: 0 !important;
  }
}
.branding-content {
  position: fixed !important;
  bottom: 72px;
  margin: 0 auto !important;
  padding: 5px;
  width: 90% !important;
  max-width: 500px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  left: 0;
  right: 0;
}
.branding-img {
  backdrop-filter: blur(10px);
}
.app__dynamic-form.hide-navbar {
  padding-top: unset !important;
}
</style>
