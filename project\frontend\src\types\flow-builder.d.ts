namespace Types {
  declare type CreatorSchemaPart = import('../helpers/creator/schema-part').CreatorSchemaPart;
  declare type CreatorSchemaPartClass =
    typeof import('../helpers/creator/schema-part').CreatorSchemaPart;
  declare type CreatorSchema = import('../helpers/creator/schema').CreatorSchema;
  declare type CreatorStep = import('../helpers/creator/steps/step').CreatorStep;
  declare type CreatorStepClass = typeof import('../helpers/creator/steps/step').CreatorStep;
  declare type CreatorItem = import('../helpers/creator/items/item').CreatorItem;
  declare type CreatorItemClass<T extends Types.ISchemaItem = Types.ISchemaItem> =
    typeof import('../helpers/creator/items/item').CreatorItem<T>;
  declare type CreatorItemFieldset =
    import('../helpers/creator/items/fieldset').CreatorItemFieldset;
  declare type CreatorStepDocument =
    import('../helpers/creator/steps/ekyc/document').CreatorStepDocument;
  declare type CreatorStepDocumentNext =
    import('../helpers/creator/steps/ekyc/document-next').CreatorStepDocumentNext;
  declare type CreatorStepLiveness =
    import('../helpers/creator/steps/ekyc/liveness').CreatorStepLiveness;
  declare type CreatorStepContactInfo =
    import('../helpers/creator/steps/otp/contact-info').CreatorStepContactInfo;

  declare type FlowScopeType = 'item' | 'step' | 'unknown';

  declare type FlowItemType =
    | 'item'
    | 'data_field'
    | 'fieldset'
    | 'page_break'
    | 'full_name'
    | 'divider'
    | 'image'
    | 'image_preset'
    | 'telephone'
    | 'email'
    | 'short_long_answer'
    | 'dropdown'
    | 'choice'
    | 'country'
    | 'nationality'
    | 'gender'
    | 'name_prefix'
    | 'paragraph'
    | 'address'
    | 'date'
    | 'date_of_birth'
    | 'date_of_issue'
    | 'date_of_expiry'
    | 'signature'
    | 'file_upload'
    | 'smart_uploader'
    | 'ekyc_liveness_item'
    | 'ekyc_document_item'
    | 'ekyc_liveness_item_next'
    | 'ekyc_document_item_next'
    | 'ekyc_backcard_item'
    | 'ekyc_max_attempt_warning'
    | 'document_numbers'
    | 'document_full_name'
    | 'otp_item'
    | 'otp_email'
    | 'consent'
    | 'contact_info_item'
    | 'async_validator'
    | 'bank_statement_uploader'
    | 'utility_bill'
    | 'business_information'
    | 'dynamic_result'
    | 'dynamic_result_score'
    | 'dynamic_result_criteria';

  declare type FlowStepType =
    | 'step'
    | 'input'
    | 'ekyc_liveness'
    | 'ekyc_document'
    | 'ekyc_liveness_next'
    | 'ekyc_document_next'
    | 'ekyc_document_ocr_target'
    | 'bank_statement'
    | 'address_verification'
    | 'contact_info'
    | 'telephone'
    | 'email'
    | 'otp'
    | 'page_landing'
    | 'page_thanks'
    | 'page_embed';

  declare type PossibleItemGroupType =
    | 'string'
    | 'number'
    | 'date'
    | 'boolean'
    | 'multipleSelect'
    | 'singleSelect'
    | 'any';

  declare type PossibleItemValueType = 'string' | 'number' | 'boolean' | 'date' | 'object' | 'any';

  declare type Choice<T = any> = {
    label?: string;
    icon?: string;
    image?: string;
    disabled?: boolean;
    devOnly?: boolean;
    value: T;
    children?: Types.PropertyConfigurator[];
  };

  type PropertyConfiguratorPossibleFunctionContext = {
    index?: number;
    indexList?: number[];
  };

  type PropertyConfiguratorBase<T = any> = {
    label?: string;
    placeholder?: string;
    desc?: string;
    descFooter?: string | Ref<string>;
    type: string;
    syncSchemaOptions?: Types.SyncSchemaOptions;
    inline?: boolean;
    indent?: boolean;
    tooltip?: string;
    class?: string;
    style?: string | object;
    leftIcon?: string;
    rightIcon?: string;
    validateOn?: 'before_set' | 'after_set';
    context?: PropertyConfiguratorPossibleFunctionContext;
    get?: (ctx?: PropertyConfiguratorPossibleFunctionContext) => T;
    set?: (val: T, oldVal?: T, ctx?: PropertyConfiguratorPossibleFunctionContext) => void;
    getFormatter?: (val: T) => any;
    setFormatter?: (val: T) => any;
    validate?: (
      val: T,
      oldVal?: T,
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => boolean | string;
    checkShow?: (ctx?: PropertyConfiguratorPossibleFunctionContext) => boolean;
    disabled?: () => boolean;
    onClickLeftIcon?: () => void;
    onClickRightIcon?: () => void;
    [key: string]: any;
  };
  type PropertyConfiguratorChoices<T = any> = PropertyConfiguratorBase<T> & {
    type: 'Select' | 'SingleSelectButton' | 'Radio';
    choices?: Choice<T>[] | (() => Choice<T>[]);
    inline?: boolean;
    newLine?: boolean;
  };
  type PropertyConfiguratorSelectMultipleCheckbox<T = any> = PropertyConfiguratorBase<T[]> & {
    type: 'SelectMultipleCheckbox';
    choices?: Choice<T>[] | (() => Choice<T>[]);
    searchable?: boolean;
    display?: {
      selectAllLabel?: string;
      selectSomeLabel?: string;
      noSelectLabel?: string;
      searchBoxPlaceholder?: string;
      hideIcon?: boolean;
      hidePrefixNumber?: boolean;
    };
  };
  type PropertyConfiguratorSliderChoice<T = any> = PropertyConfiguratorBase<T> & {
    type: 'SliderChoice';
    choices?: Choice<T>[];
  };
  type PropertyConfiguratorCheckboxGroup<T = any> = PropertyConfiguratorBase<T[]> & {
    type: 'CheckboxGroup';
    choices?: Choice<T>[] | (() => Choice<T>[]);
    hideCheckbox?: boolean;
  };
  declare type PropertyConfiguratorColorPicker = PropertyConfiguratorBase<string> & {
    type: 'ColorPicker';
    colorType?: string;
  };
  declare type PropertyConfiguratorShowNotice = PropertyConfiguratorBase<string> & {
    type: 'ShowNotice';
    icon?: string | false;
    level?: 'warn' | 'info';
  };
  declare type PropertyConfiguratorHtml = PropertyConfiguratorBase<string> & {
    type: 'ShowHtml';
    onClick?: Function;
  };
  declare type PropertyConfiguratorShowText = PropertyConfiguratorBase<string> & {
    type: 'ShowText';
    header?: boolean;
  };
  declare type PropertyConfiguratorShowDescription = PropertyConfiguratorBase<string> & {
    type: 'ShowDescription';
    label: string;
    desc: string;
    icon: string;
  };
  declare type PropertyConfiguratorTextbox = PropertyConfiguratorBase<string> & {
    type: 'Textbox';
    multiline?: boolean;
    maxLength?: number;
    inline?: boolean;
  };
  declare type PropertyConfiguratorPassword = PropertyConfiguratorBase<string> & {
    type: 'Password';
    maxLength?: number;
    inline?: boolean;
  };
  declare type PropertyConfiguratorTagList = PropertyConfiguratorBase<string[]> & {
    type: 'TagList';
    maxTags?: number;
    maxTagLength?: number;
    allowDuplicates?: boolean;
    choices?: string[];
  };
  declare type PropertyConfiguratorRichText = PropertyConfiguratorBase<string> & {
    type: 'RichText';
    showBasic?: boolean;
    showAlignment?: boolean;
    showList?: boolean;
    showLink?: boolean;
    showFormula?: boolean;
    showFont?: boolean;
  };
  declare type PropertyConfiguratorNumber = PropertyConfiguratorBase<number> & {
    type: 'Number';
    inline?: boolean;
    min?: number;
    max?: number;
    decimal?: number;
  };
  declare type PropertyConfiguratorTextarea = PropertyConfiguratorBase<string> & {
    type: 'Textarea';
    inline?: boolean;
  };
  declare type PropertyConfiguratorDate = PropertyConfiguratorBase<string> & {
    type: 'Date';
    configs?: {
      dateOrder?: string;
      overrideLocale?: string;
      allowNoDay?: boolean;
      allowNoMonth?: boolean;
      allowNoYear?: boolean;
      storeDateFormat?: string;
      yearFormat?: string;
      from?: Types.DateConstriantObject;
      to?: Types.DateConstriantObject;
      [key: string]: any;
    };
  };
  declare type PropertyConfiguratorCheckbox = PropertyConfiguratorBase<boolean> & {
    type: 'Checkbox';
    children?: (Types.PropertyConfigurator & { path?: string | string[] })[];
    getChildren?: (
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => (Types.PropertyConfigurator & { path?: string | string[] })[];
  };
  declare type PropertyConfiguratorSwitch = PropertyConfiguratorBase<boolean> & {
    type: 'Switch';
  };
  declare type PropertyConfiguratorCssUnit = PropertyConfiguratorBase<string> & {
    type: 'CssUnit';
  };
  declare type PropertyConfiguratorImage = PropertyConfiguratorBase<string> & {
    type: 'Image';
    previewWidth?: string;
    previewHeight?: string;
    disableRemove?: boolean;
  };
  declare type PropertyConfiguratorList = PropertyConfiguratorBase<T[]> & {
    type: 'List';
    children?: (Types.PropertyConfigurator & { path?: string | string[] })[];
    getChildren?: (
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => (Types.PropertyConfigurator & { path?: string | string[] })[];
    default?: T;
    labelList?: string;
    labelItem?: string;
    draggable?: boolean;
    expandable?: boolean;
    disableAdd?: boolean;
    disableRemove?: boolean;
    disableRemoveFirstItem?: boolean;
    emptyPlaceholder?: string;
    validateChild?: (
      val: T,
      selfIndex: number,
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => boolean | string;
  };
  declare type PropertyConfiguratorLayouts = PropertyConfiguratorBase<any> & {
    type: 'Rows' | 'Columns';
    children?: (Types.PropertyConfigurator & { path?: string | string[] })[];
    getChildren?: (
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => (Types.PropertyConfigurator & { path?: string | string[] })[];
  };
  declare type PropertyConfiguratorCollapse = PropertyConfiguratorBase<T> & {
    type: 'Collapse';
    open?: boolean;
    children?: (Types.PropertyConfigurator & { path?: string | string[] })[];
    getChildren?: (
      ctx?: PropertyConfiguratorPossibleFunctionContext,
    ) => (Types.PropertyConfigurator & { path?: string | string[] })[];
  };
  declare type PropertyConfiguratorInputOption = PropertyConfiguratorBase<T[]> & {
    type: 'InputOption';
  };

  declare type PropertyConfiguratorTableColumn = {
    label: string;
    description?: string;
    field: F;
  };
  declare type PropertyConfiguratorTableRow =
    | { label: string }
    | Record<F, Types.PropertyConfigurator[]>;
  declare type PropertyConfiguratorTable = PropertyConfiguratorBase<T[]> & {
    type: 'Table';
    columns: PropertyConfiguratorTableColumn[];
    rows: PropertyConfiguratorTableRow[];
  };
  declare type PropertyConfiguratorItemRef = PropertyConfiguratorBase<Types.RefItemInfo> & {
    type: 'ItemRef';
    allowedTypes?: FlowItemType[];
    allowedValueTypes?: PossibleItemValueType[] | Ref<PossibleItemValueType[]>;
    allowOnlyMatchedValueType?: boolean;
    collectMethod?: 'before' | 'after' | 'all';
    includeHidden?: boolean;
    excludedInstanceIds?: string[];
    excludedStepInstanceIds?: string[];
    choices?: { value: CreatorItem; label: string; disabled: boolean; hidden?: boolean }[];
    empty?: boolean;
    getEmptyValue?: () => Types.RefItemInfo | null;
    disableAutoUnsetChoice?: boolean;
  };

  declare type PropertyConfiguratorStepRef = PropertyConfiguratorBase<Types.RefStepInfo> & {
    type: 'StepRef';
    choices?: { value: CreatorItem; label: string; disabled: boolean; hidden?: boolean }[];
    empty?: boolean;
    getEmptyValue?: () => Types.RefItemInfo | null;
    excludeThanksPage?: boolean;
  };

  declare type PropertyConfigurator =
    | (PropertyConfiguratorBase & {
        type:
          | 'Divider'
          | 'ColorPicker'
          | 'Image'
          | 'Json'
          | 'Schema'
          | 'Modal'
          | 'Branding'
          | 'Time';
      })
    | PropertyConfiguratorLayouts
    | PropertyConfiguratorCollapse
    | PropertyConfiguratorShowNotice
    | PropertyConfiguratorHtml
    | PropertyConfiguratorShowText
    | PropertyConfiguratorShowDescription
    | PropertyConfiguratorRichText
    | PropertyConfiguratorChoices
    | PropertyConfiguratorSelectMultipleCheckbox
    | PropertyConfiguratorSliderChoice
    | PropertyConfiguratorCheckboxGroup
    | PropertyConfiguratorTextbox
    | PropertyConfiguratorPassword
    | PropertyConfiguratorNumber
    | PropertyConfiguratorTextarea
    | PropertyConfiguratorTagList
    | PropertyConfiguratorCheckbox
    | PropertyConfiguratorSwitch
    | PropertyConfiguratorList
    | PropertyConfiguratorInputOption
    | PropertyConfiguratorTable
    | PropertyConfiguratorCssUnit
    | PropertyConfiguratorImage
    | PropertyConfiguratorItemRef
    | PropertyConfiguratorStepRef
    | PropertyConfiguratorColorPicker;

  declare type PropertyConfiguratorCollection = {
    label: string;
    items: PropertyConfigurator[];
    devOnly?: boolean;
  };

  declare type Alignment = 'left' | 'center' | 'right';

  declare type CompleteAction = 'redirect' | 'close_tab';

  declare type AllowItemType = {
    type: FlowItemType;
    count?: number;
  };

  declare type GenerateNameOptions = {
    duplicated?: boolean;
  };

  declare type ChangeLocaleActionsOptions = {
    newLang?: string;
    oldLang?: string;
  };
  declare type SyncSchemaOptions = {
    triggerUpdate?: boolean;
    includeInstances?: string[];
  };

  declare type CollectItemsOptions = {
    pivotInstanceId?: string;
    collectMethod?: 'before' | 'after' | 'all';
    excludedInstanceIds?: string[];
    excludedStepInstanceIds?: string[];
    allowedTypes?: FlowItemType[];
    allowedValueTypes?: PossibleItemValueType[];
  };

  declare type PreviewFrameMessageData = {
    from: 'previewer';
    type: 'loaded';
    data?: {};
  };

  declare type PreviewFrameInterface = {
    previewType: 'form' | 'page';
    formInstance: Types.DynamicFormInstance;
    schemaConfig: Ref<Types.SchemaConfigSettings>;
    changeLanguage: (lang: string) => Promise<boolean>;
  };

  declare type FieldItemInfo<
    T extends Types.CreatorItem = Types.CreatorItem,
    S extends ISchemaItem = ISchemaItem,
    K extends Types.CreatorItemClass<S> = Types.CreatorItemClass<S>,
  > = {
    item: T;
    class?: K;
    enabled: boolean;
    ghost?: boolean;
    generate_name?: boolean;
    field_name?: string;
    default?: Record<string, any>;
    base?: Record<string, any>;
  };

  declare type RefItemInfo<
    T extends Types.CreatorItem = Types.CreatorItem,
    S extends ISchemaItem = ISchemaItem,
    K extends Types.CreatorItemClass<S> = Types.CreatorItemClass<S>,
  > = {
    itemName: string;
    item?: T;
    class?: K;
    enabled: boolean;
    ghost?: boolean;
    default?: Record<string, any>;
    base?: Record<string, any>;
  };
  declare type RefStepInfo<
    T extends Types.CreatorStep = Types.CreatorStep,
    S extends ISchemaStep = ISchemaStep,
    K extends Types.CreatorStepClass<S> = Types.CreatorStepClass<S>,
  > = {
    stepName: string;
    step?: T;
    class?: K;
    enabled: boolean;
    ghost?: boolean;
    default?: Record<string, any>;
    base?: Record<string, any>;
  };
}
