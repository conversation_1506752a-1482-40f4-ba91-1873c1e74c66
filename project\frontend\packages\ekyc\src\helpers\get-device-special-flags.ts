import { validateConditionObject } from '@helpers/helpers/validation-condition-utils';

export interface DeviceSpecialFlags {
  skipLoadModel?: boolean;
  skipGpu?: boolean;
  skipCpu?: boolean;
  skipTfWasm?: boolean;
  skipTfCpu?: boolean;
  skipTestCheck?: boolean;
  forceCameraLabel?: string;
}

export interface DeviceSpecialFlagCondition {
  name: string;
  conditions: Types.DEGuardConditionItem[];
  flags: DeviceSpecialFlags;
}

export function getDeviceSpecialFlags(
  conditions: DeviceSpecialFlagCondition[],
  ua: UAParser.IResult,
) {
  const deviceFlags: DeviceSpecialFlags = {};
  conditions.forEach(flagConfig => {
    // Check if all conditions pass for this flag configuration
    const conditionsPassed = flagConfig.conditions.every(condition =>
      validateConditionObject(condition, ua),
    );

    if (conditionsPassed) {
      Object.assign(deviceFlags, flagConfig.flags);
    }
  });
  return deviceFlags;
}

export function getDeviceSpecialFlagsForLivenessModel(ua: UAParser.IResult) {
  return getDeviceSpecialFlags(DEVICE_SPECIAL_LIVENESS_MODEL_FLAGS_CONDITIONS, ua);
}

export function getDeviceSpecialFlagsForCamera(ua: UAParser.IResult) {
  return getDeviceSpecialFlags(DEVICE_SPECIAL_FLAGS_CAMERA_CONDITIONS, ua);
}

export const DEVICE_SPECIAL_LIVENESS_MODEL_FLAGS_CONDITIONS: DeviceSpecialFlagCondition[] = [
  {
    name: 'ios_16_skip_load_model',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
          value_type: 'string',
        },
        op: '==',
        expected_data: {
          value: 'iOS',
          category: 'string',
          value_type: 'string',
        },
      },
      {
        initial_data: {
          value: 'os.version',
          category: 'answer',
          value_type: 'string',
        },
        op: 'in',
        expected_data: {
          value: [
            '16.4',
            '16.4.1',
            '16.5',
            '16.5.1',
            '16.6',
            '16.6.1',
            '16.7',
            '16.7.10',
            '16.7.11',
            '16.7.4',
            '16.7.5',
            '16.7.6',
            '16.7.8',
          ],
          category: 'string',
          value_type: 'string',
        },
      },
    ],
    flags: {
      skipLoadModel: true,
    },
  },
  {
    name: 'android_10_skip_gpu',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
          value_type: 'string',
        },
        op: '<=',
        expected_data: {
          value: 'Android',
          category: 'string',
          value_type: 'string',
        },
      },
      {
        initial_data: {
          value: 'os.version',
          category: 'answer',
          value_type: 'string',
        },
        op: '<=',
        expected_data: {
          value: '10',
          category: 'string',
          value_type: 'string',
        },
      },
    ],
    flags: {
      skipGpu: true,
    },
  },
  {
    name: 'samsung_s25_skip_gpu',
    conditions: [
      {
        initial_data: {
          value: 'device.model',
          category: 'answer',
          value_type: 'string',
        },
        op: 'starts_with',
        expected_data: {
          value: 'SM-S93',
          category: 'string',
          value_type: 'string',
        },
      },
    ],
    flags: {
      skipGpu: true,
    },
  },
  {
    name: 'ios_skip_test_check',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
          value_type: 'string',
        },
        op: '==',
        expected_data: {
          value: 'iOS',
          category: 'string',
          value_type: 'string',
        },
      },
    ],
    flags: {
      skipTestCheck: true,
    },
  },
  {
    name: 'mac_skip_test_check',
    conditions: [
      {
        initial_data: {
          value: 'os.name',
          category: 'answer',
          value_type: 'string',
        },
        op: '==',
        expected_data: {
          value: 'Mac OS',
          category: 'string',
          value_type: 'string',
        },
      },
    ],
    flags: {
      skipTestCheck: true,
    },
  },
];

export const DEVICE_SPECIAL_FLAGS_CAMERA_CONDITIONS: DeviceSpecialFlagCondition[] = [];
