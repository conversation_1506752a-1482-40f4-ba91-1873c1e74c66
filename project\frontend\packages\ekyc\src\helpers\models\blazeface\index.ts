import {
  type Detection,
  FaceDetector,
  type FaceDetectorOptions,
  FilesetResolver,
} from '@mediapipe/tasks-vision';

import { getResizedResolution, resizeCanvas } from '@helpers/helpers/image-utils';

import { FaceLandmarks68 } from '@ekyc/helpers/face-api-proxies/classes/FaceLandmarks68';
import { Point } from '@ekyc/helpers/face-api-proxies/classes/Point';
import { getFrameCanvas } from '@ekyc/helpers/image-utils';

const runningMode: FaceDetectorOptions['runningMode'] = 'VIDEO';
let lastVideoTime = -1;

export async function loadDetector(
  fileset: Awaited<ReturnType<typeof FilesetResolver.forVisionTasks>>,
  delegate: FaceDetectorOptions['baseOptions']['delegate'],
) {
  const detector = await FaceDetector.createFromOptions(fileset, {
    baseOptions: {
      modelAssetPath: `https://cdn.uppass.io/ekyc/models/mediapipe-models/blaze_face_short_range.tflite`,
      delegate,
    },
    runningMode,
    /**
     * The minimum confidence score for the face detection to be considered
     * Higher values = fewer detections but faster
     * successful. Defaults to 0.5.
     */

    minDetectionConfidence: 0.5,
    /**
     * The minimum non-maximum-suppression threshold for face detection to be
     * considered overlapped. Defaults to 0.3.
     */
    minSuppressionThreshold: 0.3,
  });

  return {
    detector,
    runningMode,
  };
}

export async function loadDetectorTf(backendType: 'wasm' | 'cpu') {
  const log: Record<string, any> = {};

  const [mp, faceDetection, tf] = await Promise.all([
    import('@mediapipe/face_detection').then(module => module.default),
    import('@tensorflow-models/face-detection'),
    import('@tensorflow/tfjs-core'),
  ]);

  if (backendType === 'wasm') {
    const tfjsWasm = await import('@tensorflow/tfjs-backend-wasm');
    const versionWasm = tfjsWasm.version_wasm;
    const wasmSrc = 'https://cdn.uppass.io/ekyc/models/@tensorflow/tfjs-backend-wasm';
    const wasmUrl = `${wasmSrc}/${versionWasm}/`;
    tfjsWasm.setWasmPaths(wasmUrl);

    log.version_wasm = versionWasm;
    log.mp_version = mp.VERSION;
    log.flags = tf.ENV.getFlags();
  } else {
    const tfjsCpu = await import('@tensorflow/tfjs-backend-cpu');
    log.version_cpu = tfjsCpu.version_cpu;
    log.mp_version = mp.VERSION;
    log.flags = tf.ENV.getFlags();
  }

  await tf.setBackend(backendType);
  await tf.ready();

  const model = faceDetection.SupportedModels.MediaPipeFaceDetector;
  const detector = await faceDetection.createDetector(model, {
    runtime: 'tfjs',
    // short: < 2 m. from the camera, full: < 5 m.
    modelType: 'short',
    maxFaces: 2,
  });

  return {
    detector,
    ...log,
  };
}

export function getDetectFunction(detector: Awaited<ReturnType<typeof loadDetector>>['detector']) {
  return (video: HTMLVideoElement): Promise<Types.DetectFunctionResult[]> => {
    if (!video) {
      console.warn('Video not loaded');
      return Promise.resolve(null);
    }

    const startTimeMs = performance.now();

    function convertTo68(detection: Detection) {
      const points5 = detection.keypoints.map(p => new Point(p.x, p.y));
      const points68: Point[] = new Array(68).fill(new Point(0, 0));
      const INDEXES = [36, 45, 28, 62, 16, 0];
      points5.forEach((p, i) => {
        points68[INDEXES[i]] = p;
      });
      const lm68 = new FaceLandmarks68(points68, {
        width: video.videoWidth,
        height: video.videoHeight,
      });
      return lm68;
    }

    if (video.currentTime !== lastVideoTime) {
      lastVideoTime = video.currentTime;

      const canvas = getFrameCanvas(video);

      const faces = detector.detectForVideo(canvas, startTimeMs).detections;

      const formatted = faces.map(f => {
        const result = {
          landmarks: convertTo68(f),
          boundingBox: f.boundingBox,
          categories: f.categories,
          runningMode,
        };
        return result;
      });
      return Promise.resolve(formatted);
    }

    return Promise.resolve(null);
  };
}

export function getDetectFunctionTf(
  detector: Awaited<ReturnType<typeof loadDetectorTf>>['detector'],
) {
  return async (video: HTMLVideoElement): Promise<Types.DetectFunctionResult[]> => {
    if (!video) {
      console.warn('Video not loaded');
      return Promise.resolve(null);
    }

    if (!detector) throw new Error('Detector not loaded');
    let canvas: HTMLCanvasElement = null;

    if (video instanceof HTMLVideoElement) {
      canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d', { alpha: false });
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
    } else {
      canvas = video;
    }

    const { resizedWidth, resizedHeight } = getResizedResolution(
      { inputHeight: canvas.height, inputWidth: canvas.width },
      { maxHeight: 320 },
    );
    const resized = resizeCanvas(canvas, { width: resizedWidth, height: resizedHeight });

    const faces = await detector.estimateFaces(resized);

    function convertTo68(lm: { x: number; y: number }[]) {
      const points5 = lm.map(p => new Point(p.x / resized.width, p.y / resized.height));
      const points68: Point[] = new Array(68).fill(new Point(0, 0));
      const INDEXES = [36, 45, 28, 62, 16, 0];
      points5.forEach((p, i) => {
        points68[INDEXES[i]] = p;
      });
      const lm68 = new FaceLandmarks68(points68, { width: canvas.width, height: canvas.height });
      return lm68;
    }

    const formatted = faces.map(f => {
      const result = {
        landmarks: convertTo68(f.keypoints),
        probability: f.keypoints.map(p => p.score),
        input_width: canvas.width,
        input_height: canvas.height,
        resized_width: resized.width,
        resized_height: resized.height,
      };
      return result;
    });

    return formatted;
  };
}

export function getTestDetectFunction(
  detector: Awaited<ReturnType<typeof loadDetector>>['detector'],
) {
  return async (input: ImageBitmap) => {
    const result = detector.detectForVideo(input, 0);
    const points = result.detections[0]?.keypoints?.map(p => [p.x, p.y]);

    return points;
  };
}

export function getTestDetectFunctionTf(
  detector: Awaited<ReturnType<typeof loadDetectorTf>>['detector'],
) {
  return async (input: ImageBitmap) => {
    const results = await detector.estimateFaces(input);
    const points = results[0]?.keypoints?.map(p => [p.x, p.y]);
    return Promise.resolve(points);
  };
}
