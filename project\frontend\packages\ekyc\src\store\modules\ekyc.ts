import { defineStore } from 'pinia';

import { usePopup } from '@ekyc/composables/use-popup';
import { MediaData } from '@ekyc/lib/media-data';

const { hideAllPopup } = usePopup();

export const useEkycStore = defineStore('ekyc', () => {
  // Ref (states)
  const resultState = ref<Types.EkycResultState>('hidden');

  const agent = ref({});
  const selectorEnabled = ref<boolean>(false);
  const logUrl = ref<string>('');
  const liveness = reactive(new MediaData('liveness'));
  const document = reactive(new MediaData('document'));
  const backcard = reactive(new MediaData('backcard'));

  // Function (actions)
  const getMediaData = (key: Types.EkycMediaTypes) => ({ liveness, document, backcard })[key];

  const setSelectorEnabled = payload => {
    selectorEnabled.value = payload;
  };

  const setLogUrl = payload => {
    logUrl.value = payload;
  };

  const resetAll = () => {
    agent.value = {};
    selectorEnabled.value = false;
    logUrl.value = '';
    Object.assign(liveness, new MediaData('liveness'));
    Object.assign(document, new MediaData('document'));
    Object.assign(backcard, new MediaData('backcard'));
  };

  const setBlob = (media: Types.EkycMediaTypes, payload: Partial<MediaData>) => {
    const newPayload = {
      progress: 0,
      ...payload,
    };
    Object.assign(getMediaData(media), newPayload);
    hideAllPopup();
  };

  const setProgress = (media: Types.EkycMediaTypes, payload) => {
    getMediaData(media).progress = payload;
  };

  const mediaUploadStart = ({ media, source = null, progress = 0 }) => {
    const currentMedia = getMediaData(media);
    currentMedia.uploading = true;
    currentMedia.uploaded = false;
    currentMedia.progress = progress;
    currentMedia.cancelSource = source;
  };

  const mediaUploadSuccess = ({ media, data = false }) => {
    const currentMedia = getMediaData(media);
    currentMedia.uploading = false;
    currentMedia.uploaded = data;
    currentMedia.retries = 0;
    currentMedia.progress = 0;
  };

  const mediaUploadFail = ({ media }) => {
    const currentMedia = getMediaData(media);
    currentMedia.uploaded = false;
    currentMedia.retries += 1;
    currentMedia.progress = 0;
  };

  const mediaUploadFailMax = ({ media }) => {
    const currentMedia = getMediaData(media);
    currentMedia.uploading = false;
    currentMedia.uploaded = false;
    currentMedia.retries = 0;
    currentMedia.progress = 0;
  };

  return {
    resultState,
    agent,
    selectorEnabled,
    liveness,
    document,
    backcard,
    logUrl,

    getMediaData,
    setSelectorEnabled,
    setLogUrl,
    resetAll,
    setBlob,
    setProgress,
    mediaUploadStart,
    mediaUploadSuccess,
    mediaUploadFail,
    mediaUploadFailMax,
  };
});

export default { useEkycStore };
