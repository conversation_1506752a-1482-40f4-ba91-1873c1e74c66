import { getResizedResolution, resizeImageFileToCap } from '@helpers/helpers/image-utils';

/**
 * Convert a data URL to a Blob
 * @param {string} dataurl - The data URL to be converted.
 * @returns A blob object.
 */
export function dataURLtoBlob(dataurl: string) {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  // eslint-disable-next-line no-plusplus
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

/**
 * It takes a canvas and returns the average color of the canvas.
 * @param {HTMLCanvasElement} canvas - HTMLCanvasElement
 * @param [pixelProcessingBlockSize=5] - The number of pixels to process at a time.
 * @returns an ColorInfo object
 */
export function getColorInfo(canvas: HTMLCanvasElement, pixelProcessingBlockSize = 5) {
  const ctx = canvas.getContext('2d', { alpha: false });
  const width = canvas.width;
  const height = canvas.height;

  const data = ctx.getImageData(0, 0, width, height);
  const pixelsCount = data.data.length;
  let blocksCount = 0;
  const rgb: Types.ColorInfo = {
    r: 0,
    g: 0,
    b: 0,
    brightness: 0,
  };

  // Total value
  for (let i = 0; i < pixelsCount; i += pixelProcessingBlockSize * 4) {
    blocksCount += 1;

    const r = data.data[i];
    const g = data.data[i + 1];
    const b = data.data[i + 2];
    const avg = Math.floor((r + g + b) / 3);

    rgb.r += r;
    rgb.g += g;
    rgb.b += b;
    rgb.brightness += avg;
  }

  // Average value
  rgb.r = Math.floor(rgb.r / blocksCount);
  rgb.g = Math.floor(rgb.g / blocksCount);
  rgb.b = Math.floor(rgb.b / blocksCount);
  rgb.brightness = Math.floor(rgb.brightness / blocksCount);

  return rgb;
}

export function getElementFinalTransform(el: HTMLElement) {
  const transformStr = el.style.transform;

  const getRotation = () => {
    const rotate3dMatch = RegExp(/rotate3d\(0,\s*0,\s*1,\s*(-?\d+(?:\.\d+)?)deg\)/).exec(
      transformStr,
    );
    if (rotate3dMatch) {
      return parseFloat(rotate3dMatch[1]);
    }
    const rotateMatch = RegExp(/rotateZ\((-?\d+(?:\.\d+)?)deg\)/).exec(transformStr);
    if (rotateMatch) {
      return parseFloat(rotateMatch[1]);
    }
    return 0;
  };

  const getScale = () => {
    const scaleMatch = RegExp(/scale\((-?\d+(?:\.\d+)?)\)/).exec(transformStr);
    if (scaleMatch) {
      return parseFloat(scaleMatch[1]);
    }
    return 1;
  };

  return {
    rotation: getRotation(),
    scale: getScale(),
  };
}

export async function resizeImageFileToByteCap(
  originalBlob: Blob,
  originalFileName: string,
  targetByte: number,
) {
  console.log(`Start resize image file by byte cap from ${originalBlob?.size} to ${targetByte}`);

  const imageResizeOptions = [
    { maxWidth: 1080, minWidth: 1080, quality: 0.85 }, // This is original image setting
    { maxWidth: 1080, minWidth: 1080, quality: 0.75 },
    { maxWidth: 960, minWidth: 960 },
    { maxWidth: 960, minWidth: 960, quality: 0.85 },
    { maxWidth: 960, minWidth: 960, quality: 0.75 },
    { maxWidth: 720, minWidth: 720 },
    { maxWidth: 720, minWidth: 720, quality: 0.85 },
    { maxWidth: 720, minWidth: 720, quality: 0.75 },
    // { maxWidth: 720, minWidth: 720, quality: 0.65 },
  ];
  let resizedBlob = originalBlob;

  const file = new File([originalBlob], originalFileName, {
    type: originalBlob.type,
    lastModified: Date.now(),
  });

  if (originalBlob.size <= targetByte) {
    console.log(`Use original file size:  ${resizedBlob?.size}`);
    return originalBlob;
  }

  for (let index = 0; index < imageResizeOptions.length; index++) {
    const resizeOption = imageResizeOptions[index];
    const resizedFile = await resizeImageFileToCap(file, resizeOption);

    resizedBlob = resizedFile.blob;
    console.log(`Try resize to ${resizedBlob?.size}`, resizeOption);

    if (resizedBlob?.size <= targetByte) {
      console.log(`Final file size:  ${resizedBlob?.size}`);
      return resizedBlob;
    }
  }

  console.log(`Final file size:  ${resizedBlob?.size}`);
  return resizedBlob;
}

export function getFrameCanvas(
  video: HTMLVideoElement,
  width = video.videoWidth,
  height = video.videoHeight,
) {
  const { rotation, scale } = getElementFinalTransform(video);

  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d', { alpha: false });

  ctx.save();
  ctx.translate(canvas.width / 2, canvas.height / 2);
  ctx.rotate((rotation * Math.PI) / 180);
  ctx.drawImage(
    video,
    (-canvas.width * scale) / 2,
    (-canvas.height * scale) / 2,
    canvas.width * scale,
    canvas.height * scale,
  );
  ctx.restore();
  return canvas;
}

export async function getFrameBlob(
  video: HTMLVideoElement,
  { maxHeight = 4096, maxWidth = 4096, minWidth = 0, quality = 0.85 } = {},
) {
  const el = video;
  const imgWidth = el.videoWidth;
  const imgHeight = el.videoHeight;

  const { resizedWidth, resizedHeight } = getResizedResolution(
    { inputHeight: imgHeight, inputWidth: imgWidth },
    { maxHeight, maxWidth, minWidth },
  );

  const canvas = getFrameCanvas(video, resizedWidth, resizedHeight);

  const blob = await new Promise<Blob>(resolve => {
    canvas.toBlob(resolve, 'image/jpeg', quality);
  });
  const url = blob ? URL.createObjectURL(blob) : '';

  console.log('[getFrameBlob]', {
    options: {
      maxHeight,
      maxWidth,
      minWidth,
      quality,
    },
    result: {
      video,
      canvas,
      canvasWidth: canvas.width,
      canvasHeight: canvas.height,
    },
  });

  return {
    url,
    blob,
    canvas,
  } as Types.RecordedData;
}

export const getAveragePixelColor3x3 = (canvas: HTMLCanvasElement) => {
  try {
    const ctx = canvas.getContext('2d', { alpha: false });
    if (!ctx) return [];

    const width = canvas.width;
    const height = canvas.height;
    const gridWidth = Math.floor(width / 3);
    const gridHeight = Math.floor(height / 3);

    const matrix: string[] = [];

    for (let row = 0; row < 3; row++) {
      for (let col = 0; col < 3; col++) {
        const x = col * gridWidth;
        const y = row * gridHeight;
        const w = col === 2 ? width - x : gridWidth;
        const h = row === 2 ? height - y : gridHeight;

        const imageData = ctx.getImageData(x, y, w, h);
        const data = imageData.data;

        let r = 0,
          g = 0,
          b = 0;
        const pixelCount = data.length / 4;

        for (let i = 0; i < data.length; i += 4) {
          r += data[i];
          g += data[i + 1];
          b += data[i + 2];
        }

        r = Math.round(r / pixelCount);
        g = Math.round(g / pixelCount);
        b = Math.round(b / pixelCount);

        matrix.push('#' + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1));
      }
    }

    return matrix;
  } catch (error) {
    console.warn('Cannot calculate average pixel color.', error);
    return [];
  }
};
