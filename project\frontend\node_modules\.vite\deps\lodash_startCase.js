import {
  require_upperFirst
} from "./chunk-INNFPP6M.js";
import "./chunk-3FJH2MAX.js";
import {
  require_createCompounder
} from "./chunk-T7CKRGHI.js";
import "./chunk-TPPCP22B.js";
import "./chunk-7XLBW5XD.js";
import "./chunk-CWSHORJK.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-WI7ETHBW.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/startCase.js
var require_startCase = __commonJS({
  "node_modules/lodash/startCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var upperFirst = require_upperFirst();
    var startCase = createCompounder(function(result, word, index) {
      return result + (index ? " " : "") + upperFirst(word);
    });
    module.exports = startCase;
  }
});
export default require_startCase();
//# sourceMappingURL=lodash_startCase.js.map
