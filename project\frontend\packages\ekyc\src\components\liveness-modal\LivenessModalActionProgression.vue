<script setup lang="ts">
import { type AnimationSequence, animate } from 'motion';

import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import { useFaceAction } from '@ekyc/composables/liveness/use-face-action';
import { useLivenessState } from '@ekyc/composables/liveness/use-liveness-state';
import { useTimer } from '@ekyc/composables/liveness/use-timer';
import { playProgressLivenessDonut } from '@ekyc/helpers/animations';

import LivenessModalActionIcon from './LivenessModalActionIcon.vue';

defineProps({
  isFlipped: {
    type: Boolean,
    default: false,
  },
});

const { t } = useI18n();
const {
  isShowingInstruction,
  errorMessages,
  numActionPassed,
  isShowingActionOverlay,
  isRecording,
} = useLivenessState();
const { recordTimeMaxTicks, recordTimeLeftTicks } = useTimer();
const {
  currentAction,
  numActionsToShow,
  actionProgress,
  enableIdleOnly,
  enableFaceSize,
  actionIconSetName,
} = useFaceAction();

const actionDescription = computed(() =>
  t(`ekyc.video.actions.${currentAction.value || 'idle'}` as string),
);

const actionInstruction = computed(
  () => t(`ekyc.video.instructions.${currentAction.value || 'idle'}`) as string,
);

const progressStage = ref(0);

const showProgressSeq: AnimationSequence = [
  ['.liveness-action-progress', { opacity: 1, scale: 1 }, { duration: 0.2 }],
  ['.modal-card-title', { opacity: 0, scale: 0 }, { duration: 0.2, at: '-0.2' }],
];
const hideProgressSeq: AnimationSequence = [
  ['.liveness-action-progress', { opacity: 0, scale: 0 }, { duration: 0.2 }],
  ['.modal-card-title', { opacity: 1, scale: 1 }, { duration: 0.2, at: '-0.2' }],
];
const finishSeq: AnimationSequence = [
  ['.liveness-action-progress', { opacity: 0.75, scale: 0.75 }, { duration: 0.2 }],
];

const filteredErrorMessages = computed(() =>
  errorMessages.value.filter(
    item => item.includes('error_small_face') || item.includes('error_big_face'),
  ),
);

watch(actionProgress, progress => {
  progressStage.value = (() => {
    if (progress >= 100) return 3;
    if (progress >= 20) return 2;
    if (progress > 0) return 1;
    return 0;
  })();
});

watch(progressStage, (newStage, oldStage) => {
  if (oldStage === newStage) {
    return;
  }

  const willShowFinish = newStage >= 3;
  if (willShowFinish) {
    animate(finishSeq);
    return;
  }

  const willStartActionProgress = newStage >= 2 && newStage > oldStage;
  if (willStartActionProgress) {
    animate(showProgressSeq);
    playProgressLivenessDonut();
  } else {
    animate(hideProgressSeq);
  }
});
</script>

<template>
  <div
    v-if="isShowingActionOverlay"
    class="canvas-wrapper"
    :class="`progress-stage-${progressStage}`"
  >
    <!-- Action & Progress -->
    <div class="modal-card-head">
      <div id="top-center">
        <div class="modal-card-title">
          <!-- Instruction -->
          <div class="liveness-instruction-wrapper">
            <span v-if="enableFaceSize && filteredErrorMessages.length !== 0" class="pre-check">
              {{ t(filteredErrorMessages[0].replace('recorder', 'recorder.instructions')) }}
            </span>
            <span v-else-if="progressStage > 0" class="checking">
              {{ t('ekyc.video.actions.hold') }}
            </span>
            <template v-else-if="enableIdleOnly">
              <span v-if="!isRecording" class="pre-check">
                {{ t('ekyc.video.start_instruction') }}
              </span>
              <span v-else class="pre-check">
                {{ t('ekyc.video.actions.frame') }}
              </span>
            </template>
            <template v-else-if="!enableIdleOnly">
              <liveness-modal-action-icon
                :action-name="currentAction"
                :icon-set="actionIconSetName"
                :flipped="isFlipped"
              />
              <span>{{ actionDescription }}</span>
            </template>
          </div>
        </div>

        <!-- Action Progress circle -->
        <div class="liveness-action-progress">
          <svg :width="72" :height="72">
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop class="color-1" offset="0%" stop-color="#fff" />
                <stop class="color-2" offset="100%" stop-color="#ddd" />
              </linearGradient>
            </defs>
            <circle
              class="foreground"
              stroke="url(#gradient)"
              cx="-50%"
              cy="50%"
              pathLength="1"
              r="24"
            />
            <circle
              class="background"
              stroke="rgba(255, 255, 255, 0.2)"
              cx="-50%"
              cy="50%"
              r="24"
            />
          </svg>
          <!-- {{ actionProgress }} -->
        </div>

        <!-- Timer ticks -->
        <div v-show="!isShowingInstruction" class="liveness-timer">
          <div v-for="(tick, i) in recordTimeMaxTicks" :key="'tick' + i">
            <div
              class="tick"
              :class="{ 'current-tick': tick === recordTimeLeftTicks }"
              :style="{
                color:
                  tick <= recordTimeLeftTicks
                    ? `hsl(${(recordTimeLeftTicks / recordTimeMaxTicks) * 100},80%,60%)!important`
                    : '',
              }"
            >
              ●
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="bottom-center">
      <!-- Instruction -->
      <p v-if="!enableIdleOnly" class="liveness-instruction-text">
        {{ actionInstruction }}
      </p>
      <!-- Error messages -->
      <div v-if="errorMessages[0]" class="liveness-error-message">
        <ReactiveIcon icon="feather:alert-triangle" width="16" height="16" class="has-text-black" />
        {{ t(errorMessages[0]) }}
      </div>

      <!-- Action ticks -->
      <div v-if="!enableIdleOnly" class="liveness-action-step-wrapper">
        <div class="liveness-action-steps">
          <div
            v-for="(a, i) in numActionsToShow"
            :key="'actionNo' + i"
            :class="{
              'step-success': i < numActionPassed,
              'step-active': i === numActionPassed,
              'step-ahead': i > numActionPassed,
            }"
            class="step-item"
          >
            <span v-if="i < numActionPassed" class="mdi mdi-check" />
            <span v-else>{{ i + 1 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.canvas-wrapper .modal-card-head {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  background: linear-gradient(180deg, #14232d 0%, rgba(20, 35, 45, 0) 100%);
  &.status-warning {
    background-color: rgba(226, 196, 0, 50%);
  }
}

#top-center {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  pointer-events: none;
  min-height: 160px;

  padding: 1rem 1.5rem 1rem 1.5rem;
  align-items: center;
  height: 90%;
  width: 100%;
  z-index: 1000;

  .modal-card-title {
    display: flex;
    align-items: center;
    text-align: center;
    word-spacing: 0.25rem;
    color: #ffffff;

    position: absolute;
    left: 0;
    right: 0;
    top: -30px;
    bottom: 0;
    .pre-check {
      white-space: pre-wrap !important;
    }
    .checking {
      text-shadow: 0px 0px 16px #ffffff;
    }

    .liveness-instruction-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      margin: auto;
      line-height: 1.5;
      white-space: normal;
      text-shadow: none;
      .pre-check,
      .checking,
      span {
        white-space: normal;
        text-shadow: none;
      }
    }
  }
}

#bottom-center {
  width: 100%;
  position: fixed;
  top: 85%;
  z-index: 2;
  padding: 0 1.5rem;
  .liveness-instruction-text {
    color: #fff;
    margin-bottom: 1rem;
  }
  .liveness-error-message {
    color: var(--color-text);
    background-color: var(--color-warning);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin: auto;
    display: flex;
    max-width: 330px;
    justify-content: center;
    align-items: center;
    text-align: initial;
  }
  .liveness-action-step-wrapper {
    display: block;
    text-align: center;
    justify-content: center;
    .liveness-action-steps {
      display: flex;
      justify-content: center;
      justify-items: center;
      .step-item {
        border-radius: 25px;
        display: flex;
        width: 20px;
        height: 20px;
        margin: 1rem 5px;
        justify-content: center;
        justify-items: center;
        align-items: center;
        &.step-active,
        &.step-success {
          background-color: #fff;
        }
        &.step-ahead {
          background-color: rgba(255, 255, 255, 0.2);
        }
        span {
          font-size: 1rem;
          margin: auto;
          font-weight: bold;
          line-height: 1;
          color: #000;
          &.mdi {
            margin-top: 3px;
          }
        }
      }
    }
  }
}

.liveness-action-progress {
  @apply opacity-0;

  svg {
    circle {
      fill: transparent;
      stroke-width: 12;
      stroke-dasharray: 1;
      stroke-dashoffset: 1;
      transform: rotate(-90deg);
    }
  }
}

.liveness-timer {
  @apply flex justify-evenly max-w-[280px] mx-auto text-xl;

  .tick {
    transition: color 0.2s east-out;
  }
  .tick.current-tick {
    animation-name: tick-fade;
    animation-duration: 0.5s;
    animation-direction: alternate;
    animation-iteration-count: infinite;
  }
}

@keyframes tick-fade {
  0% {
    scale: 1;
  }
  100% {
    scale: 1.1;
  }
}
</style>
