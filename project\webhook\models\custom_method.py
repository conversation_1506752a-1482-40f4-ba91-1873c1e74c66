from django.db import models
from pydash import get
from .trigger import Trigger
import requests
import datetime
import jwt

class CustomMethod(Trigger):
    webhook = models.OneToOneField('Webhook', on_delete=models.PROTECT)
    authorization = models.CharField(max_length=191, 
        choices=Trigger.Authorization.choices, 
        default=Trigger.Authorization.NO_AUTH)
    url = models.URLField(max_length=200)
    url_get_token = models.URLField(max_length=200, null=True, blank=True)
    token_path = models.CharField(max_length=100, null=True, blank=True)
    username = models.CharField(max_length=50, null=True, blank=True)
    password = models.CharField(max_length=300, null=True, blank=True)
    props = models.JSONField(null=True, blank=True, default=dict)

    @property
    def method_type(self):
        return 'custom'

    def get_token(self, password, **kwargs):
        body = {
            'username': self.username,
            'password': password
        }
        headers = self.props.get('token_headers', {})
        response = requests.post(url=self.url_get_token, json=body, headers=headers)
        if not response.ok:
            self.task.status_code = response.status_code
            self.task.response = response.text
            return

        if not self.token_path:
            return response.text

        res = response.json()
        return  get(res, self.token_path)

    def get_cloudrun_token(self):
        url = f'http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience={self.url_get_token}' #NOSONAR
        response = requests.get(url, headers={'Metadata-Flavor': 'Google'})
        return response.text
    
    def get_jwt_token(self, password):
        '''
        {
           "jwt" : {
               "payload": {},
               "expiration_delta": -1,
               "expiration_claim": "exp", # if value is null, payload will not attach with `expiration_claim | exp`
               "algorithm": "RS256", # ref https://pyjwt.readthedocs.io/en/stable/algorithms.html

           }
        }
        '''
        jwt_props = self.props.get('jwt', {})

        payload = jwt_props.get('payload', {})
        expiration_delta = int(self.props.get('expiration_delta', 100))
        expiration_claim = self.props.get('expiration_claim', 'exp')
        if expiration_claim and expiration_delta and expiration_delta > 0:
            exp = datetime.datetime.now() 
            exp = exp + datetime.timedelta(seconds=expiration_delta)
            payload[expiration_claim] = exp

        jwt_algorithm = jwt_props.get('algorithm', 'RS256')
        key = bytes(password, 'utf-8')
        return jwt.encode(payload, key, jwt_algorithm)

    def get_header_auth(self, password):
        authorization_kwargs = {}
        if self.authorization == self.Authorization.NO_AUTH:
            pass
        elif self.authorization == self.Authorization.BASIC_AUTH:
            authorization_kwargs['auth'] = (self.username, password)
        elif self.authorization == self.Authorization.BEARER_TOKEN:
            token =  self.get_token(password)
            authorization_kwargs['headers'] = {
                'Authorization': f'Bearer {token}'
            }
        elif self.authorization == self.Authorization.GCP_SERVICE_TO_SERVICE:
            token =  self.get_cloudrun_token()
            authorization_kwargs['headers'] = {
                'Authorization': f'Bearer {token}'
            }
        elif self.authorization == self.Authorization.JWT:
            token =  self.get_jwt_token(password)
            authorization_kwargs['headers'] = {
                'Authorization': f'Bearer {token}'
            }
        return authorization_kwargs

    def get_method_trigger_kwargs(self, body=dict, password=None, content_type='application/json', overwrite_request={}, **kwargs):
        method = self.props.get('method', 'post')
        headers = self.props.get('headers', {})
        trigger_kwargs = {
            'url': self.url,
            'method': method,
            'headers': {},
        }
        authorization_kwargs = self.get_header_auth(password=password)
        trigger_kwargs.update(authorization_kwargs)
        trigger_kwargs.update(overwrite_request)
        trigger_kwargs['headers'].update(headers)

        if overwrite_request and overwrite_request.get('url'):
            self.url = overwrite_request.get('url')
        
        if method != 'get' and content_type == 'application/json':
            trigger_kwargs['json'] = body
        elif method != 'get' and content_type == 'text/plain':
            trigger_kwargs['data'] = body
            trigger_kwargs['headers']['Content-Type'] = content_type
        elif method == 'get':
            trigger_kwargs['params'] = body

        return trigger_kwargs

    def get_trigger_kwargs(self, body=dict, **kwargs):
        content_type = self.props.get('headers', {}).get('Content-Type', 'application/json')
        headers = kwargs.get('headers', {})
        content_type = headers.get('Content-Type', content_type)

        return self.get_method_trigger_kwargs(body, password=self.decrypt_password(), content_type=content_type, **kwargs)
    
    @property
    def current_detail(self):
        return {
            'webhook_name': self.webhook_name,
            'is_mockup': self.is_mockup,
            'authorization': self.authorization,
            'url': self.url,
            'url_get_token': self.url_get_token,
            'token_path': self.token_path,
            'username': self.username,
            'password': self.password,
            'props': self.props,
        }
    
    @property
    def description(self):
        return self.url
    
    
