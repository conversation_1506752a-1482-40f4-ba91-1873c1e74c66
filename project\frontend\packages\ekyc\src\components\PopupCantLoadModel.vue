<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import { useFaceApiStore } from '@ekyc/store/modules/faceAPI';

import { usePopup } from '../composables/use-popup';

const { t } = useI18n();
const { showPopupCantLoadModel } = usePopup();
const faceApiStore = useFaceApiStore();
const canLoadModel = computed<boolean>(() => faceApiStore.canLoadModel);

watch(
  canLoadModel,
  val => {
    showPopupCantLoadModel.value = !val;
  },
  { immediate: true },
);
</script>

<template>
  <div v-if="showPopupCantLoadModel" class="popup-top">
    <div class="card">
      <header class="card-header">
        <div class="card-header-title">
          <div class="brand-logo" />
          <p>{{ t('ekyc.recorder.error_liveness_load_models') }}</p>
        </div>
      </header>
    </div>
    <div class="popup-top-overlay" />
  </div>
</template>

<style lang="scss" scoped>
.popup-top .card-content .content-text {
  text-align: left;
  .instruction-header {
    margin-bottom: 1rem;
  }
  .instruction-steps {
    margin-left: 1rem;
  }
}
</style>
