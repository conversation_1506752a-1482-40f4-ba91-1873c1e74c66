<script setup lang="ts">
import { useI18n } from 'vue-i18n-composable';

import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';

import { usePopup } from '../composables/use-popup';

const { t } = useI18n();
const { showPopupCameraLockdown } = usePopup();
</script>

<template>
  <div v-if="showPopupCameraLockdown" class="modal camera-lockdown-overlay is-active">
    <div class="modal-background" />
    <div class="modal-card">
      <ReactiveIcon icon="lucide:info" class="modal-icon" />

      <div class="font-medium text-lg self-center mb-2">{{ t('ekyc.popup.lockdown.header') }}</div>
      <div class="text-sm">{{ t('ekyc.popup.lockdown.info') }}</div>

      <div class="instruction-steps">
        <div>
          <div class="font-bold">
            {{ t('ekyc.popup.lockdown.steps.1.title') }}
          </div>
          <div>
            {{ t('ekyc.popup.lockdown.steps.1.description') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal {
  @apply p-6 gap-9;
  z-index: 30000 !important;
}

.modal-background {
  background-color: rgba(40, 48, 57, 1) !important;
}

.modal-card {
  @apply w-full p-6 rounded-lg;
  @apply flex flex-col;
  @apply justify-center;
  @apply text-sm text-left font-normal;
  opacity: 0px;
}

.modal-icon {
  @apply rotate-180 !text-[var(--color-warning)];
  @apply mb-6 self-center;
  width: 90px !important;
  height: 90px !important;
  font-size: 90px !important;
}

.toggle-button {
  @apply flex justify-between items-center w-5/6;
  @apply py-2 px-4 m-auto rounded-2xl;
  color: var(--color-text);
  background-color: rgba(var(--app-primary-rgb), 0.2);
}
</style>
