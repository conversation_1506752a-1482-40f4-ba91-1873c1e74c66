import {
  require_baseExtremum
} from "./chunk-DT54FW77.js";
import {
  require_identity
} from "./chunk-64Z5HK43.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseGt.js
var require_baseGt = __commonJS({
  "node_modules/lodash/_baseGt.js"(exports, module) {
    function baseGt(value, other) {
      return value > other;
    }
    module.exports = baseGt;
  }
});

// node_modules/lodash/max.js
var require_max = __commonJS({
  "node_modules/lodash/max.js"(exports, module) {
    var baseExtremum = require_baseExtremum();
    var baseGt = require_baseGt();
    var identity = require_identity();
    function max(array) {
      return array && array.length ? baseExtremum(array, identity, baseGt) : void 0;
    }
    module.exports = max;
  }
});
export default require_max();
//# sourceMappingURL=lodash_max.js.map
