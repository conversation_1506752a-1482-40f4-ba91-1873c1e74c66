/* eslint-disable camelcase */
namespace Types {
  declare type Form = {
    can_update_after_submit: boolean;
    created_at: Date;
    highlight: boolean;
    is_active: boolean;
    log_answers: boolean;
    name: string;
    slug: string;
    updated_at: Date;
    primary_color?: string;
  };

  declare type ApplicationStatusType =
    | 'all'
    | 'ekyc:fail'
    | 'ekyc:pass'
    | 'ekyc:need_review'
    | 'incomplete'
    | 'complete';

  declare type FilteredDateFieldType = 'created_at' | 'submitted_at';
  declare type TimeSpanType =
    | 'all_time'
    | 'today'
    | 'last_7_days'
    | 'last_30_days'
    | 'this_month'
    | 'this_year'
    | 'last_3_months'
    | 'last_6_months'
    | 'last_1_year'
    | 'custom';

  declare type ApplicationFaceCompareResult = {
    score: number;
    threshold: number;
    status: boolean;
    message: 'match' | 'unmatch' | 'need_review' | string;
    css_class?: string;
    front_card_preview?: string;
    liveness_preview?: string;
  };

  declare type ApplicationEkycReportAttempt = {
    id: number;
    url?: string;
    type?: string;
    images?: {
      url: string;
      action: string;
    }[];
    status: string;
    attempt: number;
    message: string;
    created_at: string;
    errors: string[];
  };

  declare type ApplicationEkycReportResult = {
    field: string;
    document_type?: Types.EkycDocumentItemTypes;
    status: boolean;
    preview: string | false;
    message: string;
    attempts: ApplicationEkycReportAttempt[];
  };

  declare type CustomStatusOption = {
    label: string;
    value: string;
    choices: Types.Choice<string>[];
  };
  declare type BaseTimeOptions = { type?: 'string'; label: string; value: Types.TimeSpanType };
  declare type DropdownTimeOptions = { type?: 'dropdown'; label: string; value: TimeOptionsType[] };
  declare type CustomTimeOptions = { type?: 'custom'; label: string; value: 'custom' };
  declare type TimeOptions = BaseTimeOptions | DropdownTimeOptions | CustomTimeOptions;

  declare type EkycWarningMessage = {
    message: string;
    key: string;
    type?: 'pass' | 'fail' | 'warn';
  };

  declare type Application = {
    id: number;
    created_at: Date;
    updated_at: Date;
    slug: string;
    status: ApplicationStatusType;
    submitted_at: Date;
    applied_form: {
      info: {
        form: string;
        form_name: string;
        applied_form: string;
        step: string;
        section: string;
        submitted_at: Date;
      };
      active_form_url: string;
    };
    data: Record<string, number>;
    ekyc_status: string;
  };

  declare type ApplicationStatusCount = {
    'ekyc:fail'?: number;
    'ekyc:pass'?: number;
    'ekyc:need_review'?: number;
    incomplete?: number;
    complete?: number;
    all?: number;
  };

  declare type ApplicationStatusStat = {
    data: Record<string, number>;
    info: {
      diff_previous_range_percentage_status: number | '-';
      diff_previous_range_total_applications: number | '-';
      diff_previous_range_total_status: number | '-';
      percentage_status: number | '-';
      total_applications: number;
      total_status: number;
    };
    status: ApplicationStatusCount;
  };

  declare type Paginated<T> = {
    count: number;
    next: string | null;
    previous: string | null;
    results: T[];
  };

  declare type ApplicationDashboardCellPreset = {
    type:
      | 'form'
      | 'id'
      | 'date'
      | 'status'
      | 'mobile'
      | 'email'
      | 'document_number'
      | 'ekyc'
      | 'aml'
      | 'otp'
      | 'liveness'
      | 'face_compare'
      | 'submit'
      | 'warning'
      | 'latest_error';
  };

  declare type ApplicationDashboardCell = {
    label: string;
    class?: string;
    inline_label?: string;
    [key: string]: any;
    getSortKey?: (row: any) => string;
    getClass?: (row: any) => string;
    getValue?: (row: any, thisCell?: ApplicationDashboardCell) => string | number;
    getTooltip?: (row: any) => string;
  };

  declare type ApplicationDashboardCellName = ApplicationDashboardCell & {
    type: 'name';
    answer_key?: string;
  };
  declare type ApplicationDashboardCellAnswer = ApplicationDashboardCell & {
    type: 'answer';
    answer_key?: string;
  };
  declare type ApplicationDashboardCellParameter = ApplicationDashboardCell & {
    type?: 'parameter';
    answer_key?: string;
  };
  declare type ApplicationDashboardCellDecision = ApplicationDashboardCell & {
    type?: 'decision';
    key: string;
    sort_key?: string;
  };
  declare type ApplicationDashboardCellCustom = ApplicationDashboardCell & {
    type?: 'custom';
    value_formula: string;
    sort_key?: string;
  };

  declare type ApplicationDashboardCellSetting =
    | ApplicationDashboardCellPreset
    | ApplicationDashboardCellName
    | ApplicationDashboardCellAnswer
    | ApplicationDashboardCellParameter
    | ApplicationDashboardCellDecision
    | ApplicationDashboardCellCustom;

  declare type ApplicationDashboardColumnSetting = {
    label?: string;
    cells: ApplicationDashboardCellSetting[];
    show_only?: string[];
  };

  declare type ApplicationDashboardColumn = {
    label?: string;
    stylingCellType?: string;
    cells: ApplicationDashboardCell[];
    getSortKey?: (row?: any) => string;
  };

  declare type ApplicationDashboardQueryChoice = {
    label: string;
    value: string;
  };

  declare type ApplicationDashboardSetting = {
    columns: ApplicationDashboardColumnSetting[];
    columns_hidden: ApplicationDashboardColumnSetting[];
    query_answers: string[];
    query_choices: ApplicationDashboardQueryChoice[];
    filter?: CustomStatus[];
    __disable_generator: boolean;
  };

  type Ref<T> = import('vue').Ref<T>;
}
