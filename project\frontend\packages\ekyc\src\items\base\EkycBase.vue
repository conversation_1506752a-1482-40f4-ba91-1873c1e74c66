<template>
  <div>
    <PopupCantLoadModel :media="media" />
    <PopupIsDesktop :media="media" />
    <PopupCameraPermission />
    <PopupCameraLockdown />
    <PopupWebview :media="media" />
    <PopupGoToSetting :media="media" />
    <slot name="modal" />

    <template v-if="resultState === 'uploading'">
      <slot name="loader">
        <div class="ekyc-result-wrapper ekyc-action-result">
          <div class="result-image result-loading">
            <LoadingWrapper />
          </div>
        </div>
      </slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';

import LoadingWrapper from '@helpers/components/LoadingWrapper.vue';

import PopupCameraLockdown from '@ekyc/components/PopupCameraLockdown.vue';
import PopupCameraPermission from '@ekyc/components/PopupCameraPermission.vue';
import PopupCantLoadModel from '@ekyc/components/PopupCantLoadModel.vue';
import PopupGoToSetting from '@ekyc/components/PopupGoToSetting.vue';
import PopupIsDesktop from '@ekyc/components/PopupIsDesktop.vue';
import PopupWebview from '@ekyc/components/PopupWebview.vue';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

defineProps({
  media: {
    type: String as () => Types.EkycMediaTypes,
    required: true,
  },
});

const ekycStore = useEkycStore();
const { resultState } = storeToRefs(ekycStore);
</script>

<style lang="scss" scoped>
.inline-hr {
  display: flex;
  align-items: center;
  text-align: center;
  margin-bottom: 1rem;
}

.inline-hr::before,
.inline-hr::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #dee7e7;
}

.inline-hr:not(:empty)::before {
  margin-right: 1em;
}

.inline-hr:not(:empty)::after {
  margin-left: 1em;
}
</style>
