<script setup lang="ts">
import { until } from '@vueuse/core';
import get from 'lodash/get';
import merge from 'lodash/merge';

import DynamicForm from '@core/components/DynamicForm/DynamicForm.vue';
import DynamicFormDebugOverlay from '@core/components/DynamicFormDebugOverlay/DynamicFormDebugOverlay.vue';
import { useSchemaConfigFormSettings } from '@core/composables/dynamic-form-settings/use-schema-config-form-settings';
import { useDynamicEvent } from '@core/composables/use-dynamic-event';
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import { sendLog } from '@core/services/applied-form';

import { Sentry } from '@helpers/plugins/sentry';

import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { loadModel } from '@ekyc/helpers/models';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

import PopupCrossDevice from '@/components/common/PopupCrossDevice.vue';
import NavBar from '@/components/navbar/NavBar.vue';
import { TrackEventOptions, useGoogleTagManager } from '@/composables/use-google-tag-manager';
import { useNavbarControl } from '@/composables/use-navbar-control';
import { usePopupCrossDevice } from '@/composables/use-popup-cross-device';
import { hasPermission } from '@/helpers/permission-utils';
import router from '@/router';

defineOptions({
  name: 'DynamicFormView',
  beforeRouteLeave(to, from, next) {
    // Clear form data in indexedDB when out of form
    const { mainInstance } = useDynamicFormApp();
    mainInstance.value?.formData.db.files.clear();
    mainInstance.value?.formData.db.forms.clear();
    next();
  },
});

const props = defineProps({
  lang: {
    type: String,
    required: false,
    default: null,
  },
  formSlug: {
    type: String,
    required: true,
  },
  appliedFormSlug: {
    type: String,
    required: true,
  },
  stepName: {
    type: String,
    required: false,
    default: null,
  },
  sectionName: {
    type: String,
    required: false,
    default: '',
  },
});

const { navbarConfig } = useNavbarControl();
const { setTrigger } = useDynamicEvent('app.form');
const { sendGtmEvent } = useGoogleTagManager();
const { checkOpenPopupCrossDevice, getCrossDeviceScope } = usePopupCrossDevice();
const { dynamicFormApp, createFormInstance } = useDynamicFormApp();
const dynamicFormInstance = ref<Types.DynamicFormInstance>(createFormInstance());

const { schemaConfigSetting } = useSchemaConfigFormSettings(computed(() => props.formSlug));
const showBranding = computed(() => schemaConfigSetting.value?.configs?.branding?.show_branding);

const formRef = ref(null);

function sendGtmEventWithProps(additional: TrackEventOptions | Record<string, string>) {
  sendGtmEvent({
    formSlug: props.formSlug,
    appliedFormSlug: props.appliedFormSlug,
    lang: props.lang,
    stepName: props.stepName,
    sectionName: props.sectionName,
    ...additional,
  });
}

function onFetchSchema(info: Types.ISchemaAndData) {
  const log: Partial<{
    expose_form_info: any;
    expose_form_answers: any;
    cross_device: any;
    skip_preload_liveness: any;
    preload_ekyc: any;
  }> = {};
  // Hide loader
  window.df_loading_list?.remove('form');
  // Send gtag event
  sendGtmEventWithProps({
    event: 'form_started',
    action: 'started',
    category: 'form_control',
    label: `${props.lang}/${props.formSlug}/${props.appliedFormSlug} started`,
    noninteraction: true,
  });
  // Prevent if has redirect_url
  if (info.redirect_page) {
    console.log('[onFetchSchema] Redirect to', info.redirect_page);
    window.location.replace(info.redirect_page);
    return;
  }
  // Prevent if no permission
  if (!info.schema.submit_url && !hasPermission('edit_submission_detail')) {
    console.log('[onFetchSchema] No permission');
    nextTick(() => {
      dynamicFormInstance.value.formControl.submittedFunction(false);
    });
  }
  // Assign global expose
  if (info.schema_config?.configs?.global_expose?.get_form_info) {
    console.log('[onFetchSchema] Assign global form_info expose');
    log.expose_form_info = true;
    (window as any).df_get_form_info = () => dynamicFormInstance.value.getFormInfo();
  } else {
    delete (window as any).df_get_form_info;
  }
  if (info.schema_config?.configs?.global_expose?.get_form_answers) {
    console.log('[onFetchSchema] Assign global form_answers expose');
    log.expose_form_answers = true;
    (window as any).df_get_form_answers = () => dynamicFormInstance.value.inputData;
  } else {
    delete (window as any).df_get_form_answers;
  }

  // Check if should display cross device popup
  log.cross_device = checkOpenPopupCrossDevice();

  // Check if should set `_cross_device` answer (check on mobile)
  const isCrossDevice = !!+router.currentRoute.query.cross_device;
  if (isCrossDevice) {
    dynamicFormInstance.value.setFormData({ field: '_cross_device', value: true });
  }

  // ekyc background load, (only if ekyc.liveness is found, and not skipped)
  if (info.schema_config?.configs?.skip_preload_liveness) {
    console.log('[onFetchSchema] Preload ekyc skipped');
    log.skip_preload_liveness = true;
  } else {
    try {
      console.log('[onFetchSchema] Preload ekyc');
      const elements = Object.values(dynamicFormInstance.value.elements);
      const livenessItem = elements.find(e => e.configs.type.startsWith('Ekyc.Liveness'))
        ?.configs as Types.ISchemaItemEkycLiveness;
      if (livenessItem) {
        const { allSettings } = useEkycSettings();
        merge(allSettings.liveness, livenessItem);
        loadModel();
        console.log('[onFetchSchema] Preload ekyc done');
        log.preload_ekyc = true;
      } else {
        console.log('[onFetchSchema] Preload ekyc not needed');
        log.preload_ekyc = false;
      }
    } catch (e) {
      console.warn('Cannot preload ekyc:', e);
    }
  }

  sendLog(
    { formSlug: props.formSlug, appliedFormSlug: props.appliedFormSlug },
    {
      action: 'frontend_on_fetch_schema',
      step: props.stepName,
      section: props.sectionName,
      is_fail: false,
      detail: log,
    },
  );
}

function onError({ type, message }) {
  if (type === 'schema') {
    nextTick(() =>
      router.push({
        name: 'error.applicationNotFound',
        params: {
          lang: props.lang,
        },
      }),
    );
    console.error('Schema info error', message);
  }
}

onMounted(() => {
  window.df_loading_list?.add('form');

  Sentry.setContext('form', {
    formSlug: props.formSlug,
    appliedFormSlug: props.appliedFormSlug,
    lang: props.lang,
  });

  Sentry.setTags({
    formSlug: props.formSlug,
    appliedFormSlug: props.appliedFormSlug,
    lang: props.lang,
  });

  const ekycStore = useEkycStore();
  ekycStore.resetAll();

  dynamicFormInstance.value.formControl.navigationFunction = ({ step, section }) => {
    setTrigger('navigated');

    Sentry.setContext('form', {
      stepName: step.name,
      sectionName: section.name,
    });
    Sentry.setTags({
      stepName: step.name,
      sectionName: section.name,
    });

    const routingParams = {
      lang: dynamicFormApp.locale,
      formSlug: props.formSlug,
      appliedFormSlug: props.appliedFormSlug,
      stepName: step.name,
      sectionName: section.name,
    };
    sendGtmEventWithProps({
      event: 'form_navigated',
      action: 'navigated',
      category: 'form_control',
      label: `${routingParams.lang}/${routingParams.formSlug}/${routingParams.appliedFormSlug}/${routingParams.stepName}/${routingParams.sectionName}`,
      noninteraction: true,
      ...routingParams,
    });
    router.push({
      name: 'dynamic_form',
      params: routingParams,
      query: {
        ...router.currentRoute.query,
      },
    });
  };

  dynamicFormInstance.value.formControl.submittedFunction = async isFirstSubmit => {
    // Wait until formSchema is loaded before proceeding (max 1s, don't throw on timeout)
    // (Just to make sure in case that schema was actually not loaded in 1s)
    await until(() => dynamicFormInstance.value.formSchema).toBeTruthy({
      timeout: 1000,
      throwOnTimeout: false,
    });

    if (isFirstSubmit) {
      setTrigger('submitted');
      sendGtmEventWithProps({
        event: 'form_submitted',
        action: 'submitted',
        category: 'form_control',
        label: `${props.lang}/${props.formSlug}/${props.appliedFormSlug} submitted`,
        lang: dynamicFormApp.locale,
        noninteraction: true,
      });
    }

    const submittedPage = get(dynamicFormInstance.value.formSchema, 'submitted_page');
    if (submittedPage) {
      router.replace({
        name: 'Page',
        params: {
          lang: props.lang,
          formSlug: props.formSlug,
          appliedFormSlug: props.appliedFormSlug,
          pageName: submittedPage,
          data: dynamicFormInstance.value.inputData as any,
        },
        query: {
          ...router.currentRoute.query,
        },
      });
    } else {
      router.replace({
        name: 'thankyou',
      });
    }
  };

  dynamicFormInstance.value.formControl.expiredFunction = () => {
    router.replace({
      name: 'error.notFound',
      params: {
        lang: dynamicFormApp.locale,
      },
    });
  };

  // Define cross device scope
  // Register Watch to check cross device every step change
  watch(
    () => dynamicFormInstance.value?.currentStep,
    () => {
      if (getCrossDeviceScope() === 'ekyc') {
        checkOpenPopupCrossDevice();
      }
    },
  );
});
</script>

<template>
  <div
    class="app__dynamic-form"
    :class="[`form_slug__${formSlug}`, !navbarConfig?.show_navbar ? 'hide-navbar' : '']"
  >
    <NavBar v-if="!$route.meta.hide_navbar" />
    <PopupCrossDevice />
    <DynamicFormDebugOverlay />
    <main class="section">
      <div class="container">
        <DynamicForm
          ref="formRef"
          :lang="lang"
          :form-slug="formSlug"
          :applied-form-slug="appliedFormSlug"
          :step-name="stepName"
          :section-name="sectionName"
          :instance="dynamicFormInstance"
          @fetch-schema="onFetchSchema"
          @error="onError"
        />

        <div v-if="showBranding" class="branding-content container">
          <img
            class="branding-img"
            src="https://cdn.uppass.io/logos/powered-by-uppass.svg"
            alt="Powered by Uppass"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
.branding-content {
  position: fixed;
  bottom: 72px;
  margin: 0 auto;
  padding: 5px;
  width: 90%;
  max-width: 500px !important;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  left: 0;
  right: 0;
}
.branding-img {
  backdrop-filter: blur(10px);
}
.app__dynamic-form.hide-navbar {
  padding-top: unset !important;
}
</style>
