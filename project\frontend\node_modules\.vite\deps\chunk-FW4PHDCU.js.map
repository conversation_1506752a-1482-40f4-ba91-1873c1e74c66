{"version": 3, "sources": ["../../lodash/_parent.js", "../../lodash/_baseUnset.js"], "sourcesContent": ["var baseGet = require('./_baseGet'),\n    baseSlice = require('./_baseSlice');\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nmodule.exports = parent;\n", "var castPath = require('./_castPath'),\n    last = require('./last'),\n    parent = require('./_parent'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\n\nmodule.exports = baseUnset;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AAAd,QACI,YAAY;AAUhB,aAAS,OAAO,QAAQ,MAAM;AAC5B,aAAO,KAAK,SAAS,IAAI,SAAS,QAAQ,QAAQ,UAAU,MAAM,GAAG,EAAE,CAAC;AAAA,IAC1E;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,OAAO;AADX,QAEI,SAAS;AAFb,QAGI,QAAQ;AAUZ,aAAS,UAAU,QAAQ,MAAM;AAC/B,aAAO,SAAS,MAAM,MAAM;AAC5B,eAAS,OAAO,QAAQ,IAAI;AAC5B,aAAO,UAAU,QAAQ,OAAO,OAAO,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}