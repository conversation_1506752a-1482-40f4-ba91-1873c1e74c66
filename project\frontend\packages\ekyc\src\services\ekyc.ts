import { promiseTimeout } from '@vueuse/core';
import axios from 'axios';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { lib as CryptoLib } from 'crypto-js';
import AES from 'crypto-js/aes';
import BASE64 from 'crypto-js/enc-base64';
import UTF8 from 'crypto-js/enc-utf8';
import MD5 from 'crypto-js/md5';
import formatISO from 'date-fns/formatISO';

import { useUuidCookie } from '@helpers/composables/use-uuid-cookie';
import { getUserAgent } from '@helpers/helpers/user-agent';

import { useEkycSettings } from '@ekyc/composables/use-ekyc-settings';
import { makeid } from '@ekyc/helpers/utils';
import { MediaData } from '@ekyc/lib/media-data';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

import { resizeImageFileToByteCap } from '../helpers/image-utils';

class FastEncryption {
  private async getKey(key: string): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(key),
      { name: 'PBKDF2' },
      false,
      ['deriveKey'],
    );

    return window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: new TextEncoder().encode('ekyc-salt'),
        iterations: 100000,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt'],
    );
  }

  async encrypt(plaintext: string, key: string): Promise<string> {
    const cryptoKey = await this.getKey(key);
    const nonce = window.crypto.getRandomValues(new Uint8Array(12));
    const encodedData = new TextEncoder().encode(plaintext);

    const ciphertext = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: nonce,
      },
      cryptoKey,
      encodedData,
    );

    // Combine nonce and ciphertext
    const combined = new Uint8Array(nonce.length + ciphertext.byteLength);
    combined.set(nonce);
    combined.set(new Uint8Array(ciphertext), nonce.length);

    // Convert to base64
    return Buffer.from(combined).toString('base64');
  }
}

const fastCrypto = new FastEncryption();

type ApiAttemptResult = { attempt_count: number; attempt_left: number; max_attempt: number };

async function getEncryptedJson(json: Record<string, any>): Promise<string> {
  const key = makeid(16);
  const key64 = Buffer.from(key).toString('base64');
  const jsonString = JSON.stringify(json);
  const jsonStringEscaped = jsonString.replace(
    /[\u007F-\uFFFF]/g,
    x => `\\u${('000' + x.codePointAt(0).toString(16)).slice(-4)}`,
  );

  const encryptedString = await fastCrypto.encrypt(jsonStringEscaped, key);
  return `${key64}-${encryptedString}`;
}

function getSessionInfo() {
  const { getUuid } = useUuidCookie();

  const uaResult = getUserAgent();
  return {
    device_type: uaResult.device.type,
    session_ua: uaResult.ua,
    session_browser_uuid: getUuid(),
  };
}

async function getFileHashes(blobs: Blob[]) {
  const promises = blobs.map(
    b =>
      new Promise<string>(resolve => {
        const reader = new FileReader();
        reader.readAsArrayBuffer(b);
        reader.onloadend = () => {
          const hash = MD5(CryptoLib.WordArray.create(reader.result as any)).toString();
          resolve(hash);
        };
      }),
  );
  const filehashes = await Promise.all(promises);
  return filehashes;
}

type SendMediaPayload = {
  media: Types.EkycMediaTypes;
  selectedCountry: string;
  acceptedDocumentTypes: string[];
  nonce: string;
  actionLogs?: any[];
  processLogs?: [/* action */ string, /* timestamp */ string, /* meta */ any?][];
  submitLog?: any;
};

export async function sendMedia({
  media,
  selectedCountry,
  acceptedDocumentTypes,
  actionLogs = [],
  nonce = makeid(5),
  processLogs = [],
  submitLog = {},
}: SendMediaPayload): Promise<
  (
    | {
        data: Partial<
          typeof media extends 'liveness'
            ? Types.EkycLivenessApiResult
            : Types.EkycDocumentApiResult
        >;
        success: true;
      }
    | { data: string; success: false }
  ) & {
    actionLogs: any[];
    processLogs: any[];
  }
> {
  processLogs.push(['frontend_start', formatISO(new Date(), { format: 'extended' })]);

  const ekycStore = useEkycStore();
  const mediaData: MediaData = ekycStore.getMediaData(media);

  if (!mediaData.blobs) {
    return {
      data: null,
      success: false,
      actionLogs,
      processLogs,
    };
  }
  const { allSettings } = useEkycSettings();

  const url = allSettings[media].upload_url || allSettings[media].auto_url;
  const shouldSendLogSeperately = actionLogs.length > 1;

  if (!url) {
    throw new Error('No upload url provided');
  }

  // Set/Reset variables
  const source = axios.CancelToken.source();
  ekycStore.mediaUploadStart({ media, source });

  // FormData
  const form = new FormData();
  const mediaMaxFileSize = allSettings[media]?.max_memory_size;
  const encryptedBlobs: Blob[] = [];
  for (let i = 0; i < mediaData.blobs.length; i += 1) {
    // Reduce size
    let targetBlob = mediaData.blobs[i];
    if (mediaMaxFileSize) {
      const resizedBlob = await resizeImageFileToByteCap(
        mediaData.blobs[i],
        mediaData.filenames[i],
        mediaMaxFileSize,
      );
      targetBlob = resizedBlob;
    }
    const blobSizeInBytes = targetBlob.size;
    console.log(`Blob size of ${mediaData.filenames[i]}: ${blobSizeInBytes} bytes`);
    // Encrypt
    const fileReader = new FileReader();
    fileReader.readAsArrayBuffer(targetBlob);
    await new Promise<void>(resolve => {
      fileReader.onload = async () => {
        const key = makeid(16);
        const key64 = Buffer.from(key).toString('base64');
        const fileData = Buffer.from(fileReader.result as ArrayBuffer).toString('base64');
        const encryptedFile = await fastCrypto.encrypt(fileData, key);
        const encryptedFileData = new Blob([`${key64}-${encryptedFile}`]);
        encryptedBlobs.push(encryptedFileData);
        form.append(`files[${i}]`, encryptedFileData, mediaData.filenames[i]);
        resolve();
      };
    });
  }

  // JSON Data
  const json = {
    ...getSessionInfo(),
    filehashes: await getFileHashes(encryptedBlobs),
    nonce,
    logs: shouldSendLogSeperately ? [] : actionLogs,
    ensure_facecompare: allSettings[media].ensure_facecompare,
    accepted_countries: selectedCountry ? [selectedCountry] : undefined,
    accepted_document_types: acceptedDocumentTypes,
    orientation: 'vertical',
    process_logs: processLogs,
    submit_log: submitLog,
  };

  const encryptedJson = await getEncryptedJson(json);
  form.append('data', encryptedJson);

  processLogs.push([
    'upload_to_backend',
    formatISO(new Date()),
    {
      enc_json_length: encryptedJson.length,
      blob_infos: mediaData.blobs.map(blob => ({
        size: blob.size,
        type: blob.type,
      })),
    },
  ]);

  // Post Request
  try {
    const res = await axios.post(url, form, {
      onUploadProgress: e => {
        ekycStore.setProgress(media, (e.loaded / e.total) * 100);
      },
      cancelToken: source.token,
    });

    // Post Success
    if (res.data.code === 200) {
      processLogs.push(['success', formatISO(new Date())]);
      ekycStore.mediaUploadSuccess({ media, data: res.data });
      return {
        data: res.data,
        success: true,
        actionLogs,
        processLogs,
      };
    }
    throw res.data;
  } catch (error) {
    processLogs.push(['error', formatISO(new Date()), { error }]);
    console.log('sendMedia Error:', error);

    ekycStore.mediaUploadFail({ media });

    if (axios.isCancel(error)) {
      processLogs.push(['cancel', formatISO(new Date())]);
      console.log('Canceled');
      ekycStore.mediaUploadFailMax({ media });
      return {
        data: 'Cancelled',
        success: false,
        actionLogs,
        processLogs,
      };
    }

    // Retry if not max, no error data recieved (probably network error)
    const errorData = error?.response?.data;
    if (mediaData.retries <= allSettings[media].retryMax && !errorData) {
      console.log('Retry uploading', { media, url, nonce }, mediaData.retries);
      processLogs.push(['retry_delay_started', formatISO(new Date())]);
      await promiseTimeout(allSettings[media].retryDelay);
      processLogs.push(['retry_delay_finished', formatISO(new Date())]);
      return sendMedia({
        media,
        nonce,
        selectedCountry,
        acceptedDocumentTypes,
        actionLogs,
        processLogs,
        submitLog,
      });
    }

    // Max Retry
    // console.log('Max Retry')
    ekycStore.mediaUploadFailMax({ media });
    return {
      data: errorData ?? error,
      success: false,
      actionLogs,
      processLogs,
    };
  } finally {
    // Send log seperately
    if (shouldSendLogSeperately) {
      console.log('shouldSendLogSeperately', actionLogs);
      processLogs.push([
        'send_log_seperately',
        formatISO(new Date()),
        { logCount: actionLogs.length },
      ]);
      const logUrl = allSettings.liveness.liveness_log_url;
      axios.post(logUrl, actionLogs).catch(error => {
        processLogs.push(['error_send_log_seperately', formatISO(new Date()), { error }]);
        console.log('shouldSendLogSeperately Error:', error);
      });
    }
  }
}

export function cancelUpload(media: Types.EkycMediaTypes) {
  const ekycStore = useEkycStore();
  const mediaData = ekycStore.getMediaData(media);

  if (mediaData.cancelSource) {
    mediaData.cancelSource.cancel();
    ekycStore.setProgress(media, 0);
  }
}

export async function sendNfcApi(result: object, token?: string) {
  try {
    const { allSettings } = useEkycSettings();

    // JSON Data
    const json = {
      result,
      process_logs: [],
    };

    const encryptedJson = await getEncryptedJson(json);
    const form = new FormData();
    form.append('data', encryptedJson);

    // Post Request
    const url = allSettings.document.nfc_url;
    const res = await axios.post<Types.EkycDocumentApiResult>(url, form, {
      headers: {
        'communication-token': token,
      },
    });
    console.log('sendNfcApi success', JSON.stringify(res.data, null, 2));

    return {
      status: true as const,
      data: res.data,
    };
  } catch (error) {
    console.log('sendNfcApi Error:', JSON.stringify(error, null, 2));
    if (typeof error?.response?.data === 'object') {
      return {
        status: false as const,
        data: error.response?.data as Pick<
          Types.EkycDocumentApiResult,
          'attempt_count' | 'attempt_left' | 'error_type' | 'max_attempt'
        >,
      };
    }
  }
  return {
    status: false as const,
    data: {
      from: 'server',
    },
  };
}

export async function sendLivenessStart({ logs = [] }) {
  const { allSettings } = useEkycSettings();
  return axios.post<ApiAttemptResult>(allSettings.liveness.start_url, { logs });
}

export async function sendLivenessCancel({ logs = [], recordedData = [] as Types.RecordedData[] }) {
  const { allSettings } = useEkycSettings();

  // FormData
  const form = new FormData();
  for (let i = 0; i < recordedData.length; i += 1) {
    form.append(`files[${i}]`, recordedData[i].blob, recordedData[i].filename);
  }

  // JSON Data
  const json = {
    ...getSessionInfo(),
    ensure_facecompare: allSettings.liveness.ensure_facecompare,
    logs,
  };

  form.append('data', await getEncryptedJson(json));

  return axios.post<ApiAttemptResult>(allSettings.liveness.cancel_url, form);
}

export async function sendLivenessFail({ logs = [], recordedData = [] as Types.RecordedData[] }) {
  const { allSettings } = useEkycSettings();

  // FormData
  const form = new FormData();
  for (let i = 0; i < recordedData.length; i += 1) {
    form.append(`files[${i}]`, recordedData[i].blob, recordedData[i].filename);
  }

  // JSON Data
  const uaResult = getUserAgent();
  const json = {
    device_type: uaResult.device.type,
    ensure_facecompare: allSettings.liveness.ensure_facecompare,
    logs,
  };

  form.append('data', await getEncryptedJson(json));

  return axios.post<ApiAttemptResult>(allSettings.liveness.fail_url, form);
}

export async function sendEkycLog(log) {
  const { allSettings } = useEkycSettings();
  const url = allSettings.liveness.log_url || allSettings.document.log_url;
  if (url) {
    return axios.post(url, log);
  } else {
    return Promise.resolve(null);
  }
}

export async function getActionList({ log = {} }) {
  const { allSettings } = useEkycSettings();
  console.log('getActionList log:', log);
  return axios
    .post<string>(allSettings.liveness.face_actions_url || '/ekyc/faceactions', { log })
    .then(result => {
      const base64 = result.data;
      const [key64, aes64] = base64.split('-');

      const aes = BASE64.parse(aes64);
      const key = BASE64.parse(key64);

      // split IV and ciphertext
      const iv = aes.clone();
      iv.sigBytes = 16;
      iv.clamp();
      aes.words.splice(0, 4); // delete 4 words = 16 bytes
      aes.sigBytes -= 16;

      // decryption
      const decrypted = AES.decrypt({ ciphertext: aes } as any, key, {
        iv,
      });
      const json = decrypted.toString(UTF8);
      const decryptedResult = JSON.parse(json);

      allSettings.liveness.upload_url = decryptedResult.upload_url;
      allSettings.liveness.liveness_log_url = decryptedResult.log_url;
      allSettings.liveness.result_tracking_url = decryptedResult.result_tracking_url;
      allSettings.liveness.start_url = decryptedResult.start_url;
      allSettings.liveness.cancel_url = decryptedResult.cancel_url;
      allSettings.liveness.fail_url = decryptedResult.fail_url;

      const actionList: Types.FaceActionPossibleValue[][] = decryptedResult.liveness_actions.map(
        (actionOrActions: string | string[]) =>
          Array.isArray(actionOrActions) ? actionOrActions : [actionOrActions],
      );
      const actionSequence: Types.ActionSequencePossibleValue[] = decryptedResult.action_sequence;

      return { actionList, actionSequence };
    });
}

export async function checkQuality({ recordedData = {} as Types.RecordedData, url = '' }) {
  if (!recordedData.blob) {
    return Promise.resolve(null);
  }
  if (!url) {
    throw new Error('No upload url provided');
  }

  // FormData
  const form = new FormData();
  form.append('files[0]', recordedData.blob, recordedData.filename);

  // JSON Data
  const json = {
    ...getSessionInfo(),
    filehashes: await getFileHashes([recordedData.blob]),
  };
  form.append('data', await getEncryptedJson(json));

  return axios.post(url, form);
}
