<script setup lang="ts">
import { templateRef } from '@vueuse/core';
import { type animate } from 'motion';

import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import {
  playProgressUploadStage1,
  playProgressUploadStage2,
  prepareProgressUpload,
} from '@ekyc/helpers/animations';

const props = defineProps({
  progress: {
    type: Number,
    default: 0,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  showSubtitle: {
    type: Boolean,
    default: true,
  },
  showProgress: {
    type: <PERSON>olean,
    default: false,
  },
});

const { t } = useI18n();
const rootRef = templateRef('rootRef');

let controller1: ReturnType<typeof animate>;
let controller2: ReturnType<typeof animate>;

async function start() {
  prepareProgressUpload(rootRef.value);
  controller1 = playProgressUploadStage1(
    rootRef.value,
    computed(() => props.progress),
  );
  controller1.then(() => {
    controller2 = playProgressUploadStage2(rootRef.value);
  });
}

async function complete() {
  if (controller1) {
    controller1.speed = 100;
    await controller1.finished;
  }
  if (controller2) {
    controller2.speed = 20;
    await controller2.finished;
  }
}

defineExpose({
  start,
  complete,
});
</script>

<template>
  <div ref="rootRef" class="uploading-overlay">
    <svg :width="72" :height="72">
      <circle
        class="foreground"
        stroke="var(--app-primary-color, #4364E8)"
        cx="-50%"
        cy="50%"
        r="24"
        pathLength="1"
      />
      <circle
        class="background"
        stroke="var(--app-primary-color, #4364E8)"
        cx="-50%"
        cy="50%"
        r="24"
        pathLength="1"
        opacity="0.2"
        start-offset="1"
      />
    </svg>

    <div v-if="showTitle" class="verifying-titles" :class="{ 'has-subtitle': showSubtitle }">
      <div class="uploading-1">{{ t('ekyc.recorder.uploading_liveness') }}</div>
      <div class="verifying-1">{{ t('ekyc.recorder.verifying_card') }}</div>
      <div class="verifying-2">{{ t('ekyc.recorder.verifying_card_2') }}</div>
    </div>

    <div v-if="showSubtitle" class="verifying-subtitle">
      {{ t('ekyc.recorder.please_wait') }}
    </div>

    <div v-if="showProgress" class="text-white">{{ progress }}%</div>
  </div>
</template>

<style lang="scss" scoped>
.uploading-overlay {
  @apply absolute w-full h-full z-30;
  @apply flex flex-col items-center justify-center gap-6;

  svg {
    circle {
      fill: transparent;
      stroke-width: 12;
      stroke-dasharray: 1;
      transform: rotate(-90deg);

      &.foreground {
        stroke-dashoffset: 1;
      }
      &.background {
        stroke-dashoffset: 0;
      }
    }
  }
}

.verifying-titles {
  @apply font-medium text-sm leading-6 h-9;
  > * {
    @apply absolute left-0 right-0;
  }
  > .verifying-1 {
    @apply opacity-0;
  }
  > .verifying-2 {
    @apply opacity-0;
  }
}

.verifying-titles.has-subtitle {
  @apply text-xl leading-8;
}

.verifying-subtitle {
  @apply font-normal text-sm;
}
</style>
