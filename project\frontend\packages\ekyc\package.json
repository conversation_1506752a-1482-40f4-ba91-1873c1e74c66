{"name": "@creditok/ekyc", "version": "0.3.24", "license": "MIT", "main": "src/index", "module": "src/index", "unpkg": "src/index", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "files": ["dist/**/*", "src/**/*"], "dependencies": {"@creditok/dynamic-form-helpers": "^1.0.8", "@mediapipe/face_detection": "0.4.**********", "@mediapipe/face_mesh": "0.4.**********", "@mediapipe/tasks-vision": "0.10.21", "@tensorflow-models/face-detection": "^1.0.3", "@tensorflow-models/face-landmarks-detection": "^1.0.6", "@tensorflow/tfjs-backend-cpu": "4.22.0", "@tensorflow/tfjs-backend-wasm": "4.22.0", "@tensorflow/tfjs-converter": "4.22.0", "@tensorflow/tfjs-core": "4.22.0", "@vueuse/core": "~11.3.0", "@vueuse/integrations": "~11.3.0", "axios": "^1.11.0", "blob-to-buffer": "^1.2.9", "crypto-js": "^4.2.0", "date-fns": "^2.29.1", "date-fns-tz": "^1.3.6", "dompurify": "^3.2.3", "lodash": "^4.17.21", "motion": "^12.23.12", "pinia": "^2.3.1", "register-service-worker": "^1.7.2", "vue": "^2.7.16", "vue-i18n": "^8.28.2", "webrtc-adapter": "^9.0.3"}, "devDependencies": {"@types/dompurify": "^3.2.0", "@types/webrtc": "^0.0.46", "@vitejs/plugin-vue2": "^2.3.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "unplugin-auto-import": "^20.1.0", "vite": "^7.1.3", "vite-plugin-circular-dependency": "^0.5.0"}, "resolutions": {"**/vue-demi": "*"}}